import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useState } from "react";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import {
  Plus,
  AppWindow,
  Settings,
  Trash2,
  Bot,
  FileText,
  Stethoscope,
  MessageSquare,
  Building2,
  Eye,
  Edit,
  Check,
  X,
} from "lucide-react";
import { CreateApplicationDialog } from "@/src/features/registration/components/CreateApplicationDialog";
import {
  useApplications,
  useApplicationStats,
  useDeleteApplication,
  useApproveApplication,
  getApplicationStatusInfo,
  getApplicationTypeInfo,
} from "@/src/features/registration/hooks/useApplications";
import type { ApplicationType } from "@langfuse/shared/src/db";

export default function ApplicationRegistrationPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // 获取应用数据
  const { data: applicationsData, isLoading: applicationsLoading } =
    useApplications({
      projectId,
      limit: 50,
    });

  // 获取统计数据
  const { data: statsData, isLoading: statsLoading } =
    useApplicationStats(projectId);

  // 删除应用功能
  const deleteApplication = useDeleteApplication();

  // 审核应用功能
  const approveApplication = useApproveApplication();

  const applications = applicationsData?.applications || [];
  const stats = statsData || {
    totalCount: 0,
    activeCount: 0,
    pendingCount: 0,
    totalUsage: 0,
    activeRate: 0,
  };

  const handleDeleteApplication = async (applicationId: string) => {
    if (confirm("确定要删除这个应用吗？此操作无法撤销。")) {
      try {
        await deleteApplication.mutateAsync({
          projectId,
          applicationId,
        });
      } catch (error) {
        console.error("删除应用失败:", error);
      }
    }
  };

  const handleApproveApplication = async (
    applicationId: string,
    approved: boolean,
  ) => {
    const action = approved ? "通过" : "拒绝";
    const reason = approved ? undefined : prompt(`请输入拒绝原因（可选）:`);

    if (confirm(`确定要${action}这个应用吗？`)) {
      try {
        await approveApplication.mutateAsync({
          projectId,
          applicationId,
          approved,
          reason: reason || undefined,
        });
      } catch (error) {
        console.error(`应用${action}失败:`, error);
      }
    }
  };

  // 查看应用详情
  const handleViewApplication = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}`,
    );
  };

  // 编辑应用
  const handleEditApplication = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}?tab=edit`,
    );
  };

  // 应用设置
  const handleApplicationSettings = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}?tab=settings`,
    );
  };

  // 应用类型图标映射
  const getAppIcon = (type: string) => {
    const iconMap: Record<string, any> = {
      "robot-application": Bot,
      "quality-control-app": FileText,
      "document-generation-app": Stethoscope,
      "intelligent-customer-service": MessageSquare,
      "intelligent-agent-app": Building2,
      "custom-application": AppWindow,
      default: AppWindow,
    };
    return iconMap[type] || iconMap.default;
  };

  const handleCreateSuccess = (newApp: any) => {
    console.log("New application created:", newApp);
    // 数据会通过React Query自动刷新
  };

  return (
    <Page
      headerProps={{
        title: t("applicationRegistration", "Application Registration"),
        help: {
          description: "管理和注册应用程序，配置客户端凭据和权限。",
          href: "https://docs.example.com/registration/applications",
        },
        actionButtonsRight: (
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            新建应用
          </Button>
        ),
      }}
    >
      <div className="space-y-6">
        {/* 概览卡片 */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总应用数</CardTitle>
              <AppWindow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCount}</div>
              <p className="text-xs text-muted-foreground">
                {statsLoading ? "加载中..." : "总应用数量"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃应用</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeCount}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeRate}% 活跃率
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总使用量</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalUsage.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">累计调用次数</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待审核</CardTitle>
              <Plus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingCount}</div>
              <p className="text-xs text-muted-foreground">等待审核应用</p>
            </CardContent>
          </Card>
        </div>

        {/* 应用列表 */}
        <Card>
          <CardHeader>
            <CardTitle>已注册应用</CardTitle>
            <CardDescription>管理您的应用程序注册和配置</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-h-[600px] overflow-y-auto">
              <div className="space-y-4">
                {applicationsLoading ? (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">加载中...</p>
                  </div>
                ) : applications.length === 0 ? (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">
                      暂无应用，点击&quot;新建应用&quot;开始创建
                    </p>
                  </div>
                ) : (
                  applications.map((app) => {
                    const AppIcon = getAppIcon(app.type);
                    const statusInfo = getApplicationStatusInfo(app.status);
                    const typeInfo = getApplicationTypeInfo(app.type);
                    return (
                      <div
                        key={app.id}
                        className="flex items-start justify-between rounded-lg border p-6 transition-shadow hover:shadow-md"
                      >
                        <div className="flex flex-1 items-start space-x-4">
                          <AppIcon className="mt-1 h-10 w-10 text-primary" />
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-semibold">
                                {app.name}
                              </h3>
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline">{app.category}</Badge>
                                <span
                                  className={`rounded-full px-2 py-1 text-xs ${
                                    statusInfo.color === "green"
                                      ? "bg-green-100 text-green-800"
                                      : statusInfo.color === "yellow"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : statusInfo.color === "red"
                                          ? "bg-red-100 text-red-800"
                                          : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {statusInfo.label}
                                </span>
                              </div>
                            </div>

                            <p className="text-sm text-muted-foreground">
                              {app.description}
                            </p>

                            <div className="mb-2 flex flex-wrap gap-1">
                              {app.tags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground md:grid-cols-4">
                              <div>
                                <span className="font-medium">版本:</span>{" "}
                                {app.version}
                              </div>
                              <div>
                                <span className="font-medium">开发商:</span>{" "}
                                {app.developer}
                              </div>
                              <div>
                                <span className="font-medium">使用量:</span>{" "}
                                {app.usageCount.toLocaleString()}
                              </div>
                              <div>
                                <span className="font-medium">客户端ID:</span>{" "}
                                {app.clientId}
                              </div>
                            </div>

                            <div className="flex items-center justify-between border-t pt-2 text-xs text-muted-foreground">
                              <span>
                                创建时间:{" "}
                                {new Date(app.createdAt).toLocaleDateString()}
                              </span>
                              <span>
                                最后更新:{" "}
                                {new Date(app.updatedAt).toLocaleDateString()}
                              </span>
                              {app.isPublic && (
                                <Badge variant="outline" className="text-xs">
                                  公开应用
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="ml-4 flex items-center space-x-2">
                          {/* 审核按钮 - 只对待审核状态的应用显示 */}
                          {app.status === "PENDING" && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                title="通过审核"
                                onClick={() =>
                                  handleApproveApplication(app.id, true)
                                }
                                disabled={approveApplication.isPending}
                                className="text-green-600 hover:bg-green-50 hover:text-green-700"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                title="拒绝审核"
                                onClick={() =>
                                  handleApproveApplication(app.id, false)
                                }
                                disabled={approveApplication.isPending}
                                className="text-red-600 hover:bg-red-50 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </>
                          )}

                          <Button
                            variant="outline"
                            size="sm"
                            title="查看详情"
                            onClick={() => handleViewApplication(app.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            title="编辑应用"
                            onClick={() => handleEditApplication(app.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            title="应用设置"
                            onClick={() => handleApplicationSettings(app.id)}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            title="删除应用"
                            onClick={() => handleDeleteApplication(app.id)}
                            disabled={deleteApplication.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 创建应用弹窗 */}
      <CreateApplicationDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
