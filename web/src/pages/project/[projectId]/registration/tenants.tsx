import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useState } from "react";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Building2, Users, Settings, Hospital } from "lucide-react";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/src/components/ui/tabs";
import { TenantManagementList } from "@/src/features/tenant-management/components/TenantManagementList";
import { TenantRegistrationForm } from "@/src/features/tenant-management/components/TenantRegistrationForm";
import { TenantDetails } from "@/src/features/tenant-management/components/TenantDetails";
import { TenantEdit } from "@/src/features/tenant-management/components/TenantEdit";
import { useTenantStats } from "@/src/features/tenant-management/hooks/useTenantManagement";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";

export default function TenantRegistrationPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);

  const [activeTab, setActiveTab] = useState("overview");
  const [showRegistrationDialog, setShowRegistrationDialog] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  // 获取租户统计数据
  const { data: systemStats, isLoading: statsLoading } =
    useTenantStats(undefined);

  const handleViewTenant = (tenantId: string) => {
    console.log("handleViewTenant called with tenantId:", tenantId);
    setSelectedTenantId(tenantId);
    setActiveTab("details");
  };

  const handleEditTenant = (tenantId: string) => {
    console.log("handleEditTenant called with tenantId:", tenantId);
    setSelectedTenantId(tenantId);
    setActiveTab("edit");
  };

  const handleRegistrationSuccess = () => {
    setShowRegistrationDialog(false);
    // 刷新数据或显示成功消息
  };

  return (
    <Page
      headerProps={{
        title: t("tenantRegistration", "医院租户管理"),
        help: {
          description:
            "管理医院租户注册、审批和配置，支持多租户环境的资源分配。",
          href: "https://docs.example.com/registration/tenants",
        },
        actionButtonsRight: (
          <Button onClick={() => setShowRegistrationDialog(true)}>
            <Hospital className="mr-2 h-4 w-4" />
            注册新医院
          </Button>
        ),
      }}
    >
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="tenants">租户列表</TabsTrigger>
          {selectedTenantId && (
            <>
              <TabsTrigger value="details">租户详情</TabsTrigger>
              <TabsTrigger value="edit">编辑租户</TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* 概览卡片 */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总医院数</CardTitle>
                <Hospital className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsLoading
                    ? "..."
                    : systemStats && "totalTenants" in systemStats
                      ? systemStats.totalTenants
                      : 0}
                </div>
                <p className="text-xs text-muted-foreground">已注册医院</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃医院</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsLoading
                    ? "..."
                    : systemStats && "activeTenants" in systemStats
                      ? systemStats.activeTenants
                      : 0}
                </div>
                <p className="text-xs text-muted-foreground">正常运营中</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">待审核</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsLoading
                    ? "..."
                    : systemStats && "pendingTenants" in systemStats
                      ? systemStats.pendingTenants
                      : 0}
                </div>
                <p className="text-xs text-muted-foreground">等待审批</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总申请数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsLoading
                    ? "..."
                    : systemStats && "totalApplications" in systemStats
                      ? systemStats.totalApplications
                      : 0}
                </div>
                <p className="text-xs text-muted-foreground">应用申请</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tenants" className="space-y-4">
          {/* 租户管理列表 */}
          <TenantManagementList
            onViewTenant={handleViewTenant}
            onEditTenant={handleEditTenant}
          />
        </TabsContent>

        {selectedTenantId && (
          <>
            <TabsContent value="details" className="space-y-4">
              <TenantDetails
                tenantId={selectedTenantId}
                onBack={() => {
                  setSelectedTenantId(null);
                  setActiveTab("tenants");
                }}
                onEdit={() => setActiveTab("edit")}
              />
            </TabsContent>

            <TabsContent value="edit" className="space-y-4">
              <TenantEdit
                tenantId={selectedTenantId}
                onBack={() => setActiveTab("details")}
                onSuccess={() => {
                  setActiveTab("details");
                  // 可以添加成功提示
                }}
              />
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* 注册对话框 */}
      <Dialog
        open={showRegistrationDialog}
        onOpenChange={setShowRegistrationDialog}
      >
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>医院租户注册</DialogTitle>
            <DialogDescription>
              请填写医院的详细信息以完成租户注册
            </DialogDescription>
          </DialogHeader>
          <TenantRegistrationForm
            onSuccess={handleRegistrationSuccess}
            onCancel={() => setShowRegistrationDialog(false)}
          />
        </DialogContent>
      </Dialog>
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
