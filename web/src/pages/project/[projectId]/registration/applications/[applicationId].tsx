import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useState, useEffect } from "react";
import {
  useApplication,
  useUpdateApplication,
  getApplicationStatusInfo,
  getApplicationTypeInfo,
} from "@/src/features/registration/hooks/useApplications";
import type { ApplicationStatus } from "@langfuse/shared/src/db";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/src/components/ui/tabs";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Switch } from "@/src/components/ui/switch";
import {
  ArrowLeft,
  Bot,
  FileText,
  Stethoscope,
  MessageSquare,
  Building2,
  AppWindow,
  Edit,
  Save,
  X,
  Settings,
  BarChart3,
  Users,
  Key,
  Shield,
  Activity,
  Clock,
  TrendingUp,
  Calendar,
  AlertCircle,
} from "lucide-react";

export default function ApplicationDetailPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const applicationId = router.query.applicationId as string;
  const { t } = useTranslation(["common", "registration"]);
  const [activeTab, setActiveTab] = useState("overview");
  const [isEditingBasic, setIsEditingBasic] = useState(false);
  const [isEditingSettings, setIsEditingSettings] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    version: "",
    developer: "",
    category: "",
    tags: [] as string[],
    isPublic: false,
    autoApprove: false,
    serviceConfig: {
      endpoint: "",
      timeout: 30000,
      retryCount: 3,
    },
  });

  // 获取应用数据
  const {
    data: application,
    isLoading,
    error,
  } = useApplication(projectId, applicationId);
  const updateApplication = useUpdateApplication();

  // 根据URL参数设置活动标签
  useEffect(() => {
    const tab = router.query.tab as string;
    if (tab) {
      setActiveTab(tab);
    }
  }, [router.query.tab]);

  // 初始化表单数据
  useEffect(() => {
    if (application) {
      const serviceConfig =
        typeof application.serviceConfig === "string"
          ? JSON.parse(application.serviceConfig)
          : application.serviceConfig || {};

      setFormData({
        name: application.name || "",
        description: application.description || "",
        version: application.version || "",
        developer: application.developer || "",
        category: application.category || "",
        tags: application.tags || [],
        isPublic: application.isPublic || false,
        autoApprove: application.autoApprove || false,
        serviceConfig: {
          endpoint: serviceConfig.endpoint || "",
          timeout: serviceConfig.timeout || 30000,
          retryCount: serviceConfig.retryCount || 3,
        },
      });
    }
  }, [application]);

  // 处理返回
  const handleBack = () => {
    router.push(`/project/${projectId}/registration/applications`);
  };

  // 处理基本信息保存
  const handleSaveBasic = async () => {
    if (!application) return;

    try {
      await updateApplication.mutateAsync({
        projectId: projectId, // protectedProjectProcedure 需要这个参数
        applicationId: application.id,
        name: formData.name,
        description: formData.description,
        version: formData.version,
        developer: formData.developer,
        tags: formData.tags,
      });
      setIsEditingBasic(false);
    } catch (error) {
      console.error("保存失败:", error);
    }
  };

  // 处理设置保存
  const handleSaveSettings = async () => {
    if (!application) return;

    try {
      await updateApplication.mutateAsync({
        projectId: projectId, // protectedProjectProcedure 需要这个参数
        applicationId: application.id,
        isPublic: formData.isPublic,
        autoApprove: formData.autoApprove,
        serviceConfig: formData.serviceConfig,
      });
      setIsEditingSettings(false);
    } catch (error) {
      console.error("保存失败:", error);
    }
  };

  // 处理取消编辑
  const handleCancelBasic = () => {
    // 重置表单数据到原始值
    if (application) {
      setFormData((prev) => ({
        ...prev,
        name: application.name || "",
        description: application.description || "",
        version: application.version || "",
        developer: application.developer || "",
        tags: application.tags || [],
      }));
    }
    setIsEditingBasic(false);
  };

  const handleCancelSettings = () => {
    // 重置表单数据到原始值
    if (application) {
      const serviceConfig =
        typeof application.serviceConfig === "string"
          ? JSON.parse(application.serviceConfig)
          : application.serviceConfig || {};

      setFormData((prev) => ({
        ...prev,
        isPublic: application.isPublic || false,
        autoApprove: application.autoApprove || false,
        serviceConfig: {
          endpoint: serviceConfig.endpoint || "",
          timeout: serviceConfig.timeout || 30000,
          retryCount: serviceConfig.retryCount || 3,
        },
      }));
    }
    setIsEditingSettings(false);
  };

  // 应用类型图标映射
  const getAppIcon = (type: string) => {
    const iconMap: Record<string, any> = {
      ROBOT_APPLICATION: Bot,
      QUALITY_CONTROL_APP: FileText,
      DOCUMENT_GENERATION_APP: Stethoscope,
      INTELLIGENT_CUSTOMER_SERVICE: MessageSquare,
      INTELLIGENT_AGENT_APP: Building2,
      CUSTOM_APPLICATION: AppWindow,
      default: AppWindow,
    };
    return iconMap[type] || iconMap.default;
  };

  // 加载状态
  if (isLoading) {
    return (
      <Page
        headerProps={{
          title: "加载中...",
        }}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="text-muted-foreground">正在加载应用详情...</p>
          </div>
        </div>
      </Page>
    );
  }

  // 错误状态
  if (error || !application) {
    return (
      <Page
        headerProps={{
          title: "应用不存在",
        }}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="mb-4 text-muted-foreground">
              {error ? "加载应用详情失败" : "应用不存在或已被删除"}
            </p>
            <Button onClick={handleBack}>返回应用列表</Button>
          </div>
        </div>
      </Page>
    );
  }

  const AppIcon = getAppIcon(application.type);
  const statusInfo = getApplicationStatusInfo(
    application.status as ApplicationStatus,
  );
  const typeInfo = getApplicationTypeInfo(application.type);

  return (
    <Page
      headerProps={{
        title: application.name,
        breadcrumb: [
          { name: "项目", href: `/project/${projectId}` },
          {
            name: "应用注册",
            href: `/project/${projectId}/registration/applications`,
          },
          { name: application.name },
        ],
        help: {
          description: "查看和管理应用程序的详细信息、配置和使用统计。",
          href: "https://docs.example.com/registration/applications",
        },
        actionButtonsRight: (
          <div className="flex items-center gap-2">
            <Button onClick={handleBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回列表
            </Button>
          </div>
        ),
      }}
    >
      <div className="space-y-6">
        {/* 标签导航 */}
        <div className="border-b">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("overview")}
              className={`border-b-2 px-1 py-2 text-sm font-medium ${
                activeTab === "overview"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:border-gray-300 hover:text-foreground"
              }`}
            >
              概览
            </button>
            <button
              onClick={() => setActiveTab("edit")}
              className={`border-b-2 px-1 py-2 text-sm font-medium ${
                activeTab === "edit"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:border-gray-300 hover:text-foreground"
              }`}
            >
              编辑
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`border-b-2 px-1 py-2 text-sm font-medium ${
                activeTab === "settings"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:border-gray-300 hover:text-foreground"
              }`}
            >
              设置
            </button>
          </nav>
        </div>

        {/* 标签内容 */}
        {activeTab === "overview" && (
          <div className="space-y-6">
            {/* 应用概览 */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    总使用量
                  </CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(application.usageCount || 0).toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">总调用次数</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    应用状态
                  </CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    <Badge
                      variant={
                        statusInfo.color === "green" ? "default" : "secondary"
                      }
                      className={
                        statusInfo.color === "green"
                          ? "bg-green-100 text-green-800"
                          : ""
                      }
                    >
                      {statusInfo.label}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">当前状态</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    最后使用时间
                  </CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {application.lastUsedAt
                      ? new Date(application.lastUsedAt).toLocaleDateString()
                      : "未使用"}
                  </div>
                  <p className="text-xs text-muted-foreground">最近活动</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    应用版本
                  </CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {application.version}
                  </div>
                  <p className="text-xs text-muted-foreground">当前版本</p>
                </CardContent>
              </Card>
            </div>

            {/* 使用趋势图表 */}
            <Card>
              <CardHeader>
                <CardTitle>使用趋势</CardTitle>
                <CardDescription>查看应用的使用统计和性能指标</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex h-64 items-center justify-center text-muted-foreground">
                  使用趋势图表将在此处显示...
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 编辑标签 */}
        {activeTab === "edit" && (
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>编辑应用信息</CardTitle>
                  <CardDescription>修改应用的基本信息和配置</CardDescription>
                </div>
                {!isEditingBasic && (
                  <Button onClick={() => setIsEditingBasic(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑
                  </Button>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">应用名称</label>
                    {isEditingBasic ? (
                      <Input
                        value={formData.name}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        placeholder="输入应用名称"
                      />
                    ) : (
                      <p className="py-2 text-sm">{application.name}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">版本</label>
                    {isEditingBasic ? (
                      <Input
                        value={formData.version}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            version: e.target.value,
                          }))
                        }
                        placeholder="输入版本号"
                      />
                    ) : (
                      <p className="py-2 text-sm">{application.version}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">开发者</label>
                    {isEditingBasic ? (
                      <Input
                        value={formData.developer}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            developer: e.target.value,
                          }))
                        }
                        placeholder="输入开发者名称"
                      />
                    ) : (
                      <p className="py-2 text-sm">{application.developer}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">分类</label>
                    <p className="py-2 text-sm">{application.category}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">应用描述</label>
                  {isEditingBasic ? (
                    <Textarea
                      value={formData.description}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      placeholder="输入应用描述"
                      rows={3}
                    />
                  ) : (
                    <p className="py-2 text-sm">
                      {application.description || "暂无描述"}
                    </p>
                  )}
                </div>
                {isEditingBasic && (
                  <div className="flex items-center justify-end space-x-2">
                    <Button variant="outline" onClick={handleCancelBasic}>
                      取消
                    </Button>
                    <Button onClick={handleSaveBasic}>保存更改</Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* 设置标签 */}
        {activeTab === "settings" && (
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>应用设置</CardTitle>
                  <CardDescription>管理应用的高级设置和配置</CardDescription>
                </div>
                {!isEditingSettings && (
                  <Button onClick={() => setIsEditingSettings(true)}>
                    <Settings className="mr-2 h-4 w-4" />
                    编辑
                  </Button>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">公开应用</h4>
                      <p className="text-sm text-muted-foreground">
                        允许其他用户发现和使用此应用
                      </p>
                    </div>
                    {isEditingSettings ? (
                      <Switch
                        checked={formData.isPublic}
                        onCheckedChange={(checked) =>
                          setFormData((prev) => ({
                            ...prev,
                            isPublic: checked,
                          }))
                        }
                      />
                    ) : (
                      <p className="text-sm">
                        {application.isPublic ? "是" : "否"}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">自动审核</h4>
                      <p className="text-sm text-muted-foreground">
                        自动通过新的使用申请
                      </p>
                    </div>
                    {isEditingSettings ? (
                      <Switch
                        checked={formData.autoApprove}
                        onCheckedChange={(checked) =>
                          setFormData((prev) => ({
                            ...prev,
                            autoApprove: checked,
                          }))
                        }
                      />
                    ) : (
                      <p className="text-sm">
                        {application.autoApprove ? "是" : "否"}
                      </p>
                    )}
                  </div>
                </div>

                {/* 服务配置 */}
                <div className="space-y-4 border-t pt-4">
                  <h4 className="font-medium">服务配置</h4>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">服务端点</label>
                      {isEditingSettings ? (
                        <Input
                          value={formData.serviceConfig.endpoint}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              serviceConfig: {
                                ...prev.serviceConfig,
                                endpoint: e.target.value,
                              },
                            }))
                          }
                          placeholder="https://api.example.com"
                        />
                      ) : (
                        <p className="py-2 text-sm">
                          {(application.serviceConfig as any)?.endpoint ||
                            "未配置"}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        超时时间 (ms)
                      </label>
                      {isEditingSettings ? (
                        <Input
                          type="number"
                          value={formData.serviceConfig.timeout}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              serviceConfig: {
                                ...prev.serviceConfig,
                                timeout: parseInt(e.target.value) || 30000,
                              },
                            }))
                          }
                          placeholder="30000"
                        />
                      ) : (
                        <p className="py-2 text-sm">
                          {(application.serviceConfig as any)?.timeout || 30000}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">重试次数</label>
                      {isEditingSettings ? (
                        <Input
                          type="number"
                          value={formData.serviceConfig.retryCount}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              serviceConfig: {
                                ...prev.serviceConfig,
                                retryCount: parseInt(e.target.value) || 3,
                              },
                            }))
                          }
                          placeholder="3"
                        />
                      ) : (
                        <p className="py-2 text-sm">
                          {(application.serviceConfig as any)?.retryCount || 3}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {isEditingSettings && (
                  <div className="flex items-center justify-end space-x-2">
                    <Button variant="outline" onClick={handleCancelSettings}>
                      取消
                    </Button>
                    <Button onClick={handleSaveSettings}>保存设置</Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
