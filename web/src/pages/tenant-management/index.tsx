import { type NextPage } from "next";
import { useSession } from "next-auth/react";
import { useState } from "react";
import Head from "next/head";
import { But<PERSON> } from "@/src/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/src/components/ui/tabs";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog";
import {
  Building2,
  Users,
  FileText,
  CheckSquare,
  Plus,
  AlertCircle,
} from "lucide-react";
import { TenantRegistrationForm } from "@/src/features/tenant-management/components/TenantRegistrationForm";
import { TenantManagementList } from "@/src/features/tenant-management/components/TenantManagementList";
import { TenantDetails } from "@/src/features/tenant-management/components/TenantDetails";
import { TenantEdit } from "@/src/features/tenant-management/components/TenantEdit";
import { useTenantDashboard } from "@/src/features/tenant-management/hooks/useTenantManagement";

const TenantManagementPage: NextPage = () => {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("overview");
  const [showRegistrationDialog, setShowRegistrationDialog] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  const { systemStats, systemApplicationStats, myTasks, isLoading, error } =
    useTenantDashboard();

  // 检查用户权限
  const isSystemAdmin = session?.user?.admin;
  const canManageTenants = isSystemAdmin; // 可以根据需要扩展权限检查

  if (!session) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>请先登录以访问租户管理功能。</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!canManageTenants) {
    return (
      <div className="container mx-auto py-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            您没有权限访问租户管理功能。请联系系统管理员。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleViewTenant = (tenantId: string) => {
    console.log("handleViewTenant called with tenantId:", tenantId);
    setSelectedTenantId(tenantId);
    setActiveTab("details");
  };

  const handleEditTenant = (tenantId: string) => {
    console.log("handleEditTenant called with tenantId:", tenantId);
    setSelectedTenantId(tenantId);
    setActiveTab("edit");
  };

  const handleRegistrationSuccess = () => {
    setShowRegistrationDialog(false);
    // 可以添加成功提示或刷新数据
  };

  return (
    <>
      <Head>
        <title>租户管理 - Langfuse</title>
        <meta name="description" content="管理系统中的所有租户" />
      </Head>

      <div className="container mx-auto space-y-8 py-8">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">租户管理</h1>
            <p className="text-muted-foreground">
              管理医院租户的注册、审核和状态
            </p>
          </div>

          <div className="space-x-2">
            <Dialog
              open={showRegistrationDialog}
              onOpenChange={setShowRegistrationDialog}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  新增租户
                </Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>租户注册</DialogTitle>
                  <DialogDescription>
                    填写租户信息以创建新的租户账户
                  </DialogDescription>
                </DialogHeader>
                <TenantRegistrationForm
                  onSuccess={handleRegistrationSuccess}
                  onCancel={() => setShowRegistrationDialog(false)}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* 统计卡片 */}
        {!isLoading && systemStats && (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总租户数</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemStats && "totalTenants" in systemStats
                    ? systemStats.totalTenants
                    : 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  活跃:{" "}
                  {systemStats && "activeTenants" in systemStats
                    ? systemStats.activeTenants
                    : 0}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">待审核</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemStats && "pendingTenants" in systemStats
                    ? systemStats.pendingTenants
                    : 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  需要处理的租户申请
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">应用申请</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemApplicationStats?.totalApplications || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  待审批: {systemApplicationStats?.submittedApplications || 0}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">我的待办</CardTitle>
                <CheckSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {myTasks?.totalCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  需要处理的审批任务
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              加载数据时出现错误，请刷新页面重试。
            </AlertDescription>
          </Alert>
        )}

        {/* 主要内容区域 */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="tenants">租户列表</TabsTrigger>
            {selectedTenantId && (
              <>
                <TabsTrigger value="details">租户详情</TabsTrigger>
                <TabsTrigger value="edit">编辑租户</TabsTrigger>
              </>
            )}
            <TabsTrigger value="applications">应用申请</TabsTrigger>
            <TabsTrigger value="approvals">审批管理</TabsTrigger>
            <TabsTrigger value="analytics">数据分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* 最近的租户申请 */}
              <Card>
                <CardHeader>
                  <CardTitle>最近的租户申请</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-muted-foreground">
                    暂无数据，请查看租户列表获取详细信息。
                  </div>
                </CardContent>
              </Card>

              {/* 我的待办任务 */}
              <Card>
                <CardHeader>
                  <CardTitle>我的待办任务</CardTitle>
                </CardHeader>
                <CardContent>
                  {myTasks?.workflows && myTasks.workflows.length > 0 ? (
                    <div className="space-y-2">
                      {myTasks.workflows.slice(0, 5).map((task) => (
                        <div
                          key={task.id}
                          className="flex items-center justify-between rounded border p-2"
                        >
                          <div>
                            <div className="font-medium">{task.stepName}</div>
                            <div className="text-sm text-muted-foreground">
                              {task.tenantApplication.tenant.name}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {task.dueDate &&
                              new Date(task.dueDate).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      暂无待办任务
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tenants" className="space-y-4">
            <TenantManagementList
              onViewTenant={handleViewTenant}
              onEditTenant={handleEditTenant}
            />
          </TabsContent>

          {selectedTenantId && (
            <>
              <TabsContent value="details" className="space-y-4">
                <TenantDetails
                  tenantId={selectedTenantId}
                  onBack={() => {
                    setSelectedTenantId(null);
                    setActiveTab("tenants");
                  }}
                  onEdit={() => setActiveTab("edit")}
                />
              </TabsContent>

              <TabsContent value="edit" className="space-y-4">
                <TenantEdit
                  tenantId={selectedTenantId}
                  onBack={() => setActiveTab("details")}
                  onSuccess={() => {
                    setActiveTab("details");
                    // 可以添加成功提示
                  }}
                />
              </TabsContent>
            </>
          )}

          <TabsContent value="applications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>应用申请管理</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  应用申请管理功能正在开发中...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="approvals" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>审批管理</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  审批管理功能正在开发中...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>数据分析</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground">
                  数据分析功能正在开发中...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default TenantManagementPage;
