import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Button } from "@/src/components/ui/button";

import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Globe,
  FileText,
  User,
  Calendar,
  AlertCircle,
  ArrowLeft,
  Edit,
} from "lucide-react";
import {
  TenantTypeLabels,
  TenantStatusLabels,
  TenantStatusColors,
} from "../types";
import { useTenant } from "../hooks/useTenantManagement";

interface TenantDetailsProps {
  tenantId: string;
  onBack?: () => void;
  onEdit?: () => void;
}

export const TenantDetails: React.FC<TenantDetailsProps> = ({
  tenantId,
  onBack,
  onEdit,
}) => {
  const { data: tenant, isLoading, error } = useTenant(tenantId);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !tenant) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error ? "加载租户信息失败" : "租户不存在"}
        </AlertDescription>
      </Alert>
    );
  }

  const statusColors = TenantStatusColors;
  const statusLabels = TenantStatusLabels;
  const typeLabels = TenantTypeLabels;

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" size="sm" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          )}
          <div>
            <h2 className="text-2xl font-bold">
              {tenant.displayName || tenant.name}
            </h2>
            <p className="text-muted-foreground">{tenant.category}</p>
          </div>
        </div>
        {onEdit && (
          <Button onClick={onEdit}>
            <Edit className="mr-2 h-4 w-4" />
            编辑信息
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* 基本信息 */}
        <div className="space-y-6 lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    租户名称
                  </label>
                  <p className="mt-1">{tenant.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    显示名称
                  </label>
                  <p className="mt-1">{tenant.displayName || "-"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    机构类型
                  </label>
                  <p className="mt-1">
                    <Badge variant="outline">
                      {typeLabels[tenant.type as keyof typeof typeLabels]}
                    </Badge>
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    医院类型
                  </label>
                  <p className="mt-1">{tenant.category}</p>
                </div>
              </div>
              {tenant.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    描述
                  </label>
                  <p className="mt-1">{tenant.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 联系信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                联系信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    联系人
                  </label>
                  <p className="mt-1">{tenant.contactName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    联系电话
                  </label>
                  <p className="mt-1 flex items-center">
                    <Phone className="mr-2 h-4 w-4" />
                    {tenant.contactPhone || "-"}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  邮箱地址
                </label>
                <p className="mt-1 flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  {tenant.contactEmail}
                </p>
              </div>
              {tenant.address && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    地址
                  </label>
                  <p className="mt-1 flex items-start">
                    <MapPin className="mr-2 mt-0.5 h-4 w-4" />
                    {tenant.address}
                  </p>
                </div>
              )}
              {tenant.website && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    官方网站
                  </label>
                  <p className="mt-1 flex items-center">
                    <Globe className="mr-2 h-4 w-4" />
                    <a
                      href={tenant.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      {tenant.website}
                    </a>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 认证信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                认证信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    执业许可证号
                  </label>
                  <p className="mt-1">{tenant.licenseNumber || "-"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    税务登记号
                  </label>
                  <p className="mt-1">{tenant.taxId || "-"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    法人代表
                  </label>
                  <p className="mt-1">{tenant.legalPerson || "-"}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 状态和统计信息 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>状态信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  当前状态
                </label>
                <div className="mt-1">
                  <Badge
                    className={
                      statusColors[tenant.status as keyof typeof statusColors]
                    }
                  >
                    {statusLabels[tenant.status as keyof typeof statusLabels]}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  创建时间
                </label>
                <p className="mt-1 flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  {new Date(tenant.createdAt).toLocaleString()}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  更新时间
                </label>
                <p className="mt-1 flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  {new Date(tenant.updatedAt).toLocaleString()}
                </p>
              </div>
              {tenant.verifiedAt && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    认证时间
                  </label>
                  <p className="mt-1 flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    {new Date(tenant.verifiedAt).toLocaleString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 配额信息 */}
          <Card>
            <CardHeader>
              <CardTitle>配额信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    最大用户数
                  </label>
                  <p className="mt-1">{tenant.maxUsers || "无限制"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    最大项目数
                  </label>
                  <p className="mt-1">{tenant.maxProjects || "无限制"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    最大应用数
                  </label>
                  <p className="mt-1">{tenant.maxApplications || "无限制"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    存储限制
                  </label>
                  <p className="mt-1">
                    {tenant.storageLimit
                      ? `${tenant.storageLimit} GB`
                      : "无限制"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
