import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import { Loader2, AlertCircle, ArrowLeft, Save } from "lucide-react";
import { TenantType, TenantTypeLabels, type Tenant } from "../types";
import {
  useTenant,
  useTenantUpdate,
  useTenantErrorHandler,
} from "../hooks/useTenantManagement";

// 表单验证模式
const tenantUpdateSchema = z.object({
  name: z.string().min(1, "租户名称不能为空"),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(TenantType),
  category: z.string().min(1, "医院类型不能为空"),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url("请输入有效的网址").optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
  maxUsers: z.number().optional(),
  maxProjects: z.number().optional(),
  maxApplications: z.number().optional(),
  storageLimit: z.number().optional(),
});

type TenantUpdateFormData = z.infer<typeof tenantUpdateSchema>;

interface TenantEditProps {
  tenantId: string;
  onBack?: () => void;
  onSuccess?: (tenant: Tenant) => void;
}

export const TenantEdit: React.FC<TenantEditProps> = ({
  tenantId,
  onBack,
  onSuccess,
}) => {
  const { data: tenant, isLoading: loadingTenant } = useTenant(tenantId);
  const updateMutation = useTenantUpdate();
  const { handleError, getErrorMessage } = useTenantErrorHandler();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid, isDirty },
  } = useForm<TenantUpdateFormData>({
    resolver: zodResolver(tenantUpdateSchema),
    mode: "onChange",
  });

  // 当租户数据加载完成时，填充表单
  React.useEffect(() => {
    if (tenant) {
      setValue("name", tenant.name);
      setValue("displayName", tenant.displayName || "");
      setValue("description", tenant.description || "");
      setValue("type", tenant.type as any);
      setValue("category", tenant.category);
      setValue("contactName", tenant.contactName);
      setValue("contactEmail", tenant.contactEmail);
      setValue("contactPhone", tenant.contactPhone || "");
      setValue("address", tenant.address || "");
      setValue("website", tenant.website || "");
      setValue("licenseNumber", tenant.licenseNumber || "");
      setValue("taxId", tenant.taxId || "");
      setValue("legalPerson", tenant.legalPerson || "");
      setValue("maxUsers", tenant.maxUsers || undefined);
      setValue("maxProjects", tenant.maxProjects || undefined);
      setValue("maxApplications", tenant.maxApplications || undefined);
      setValue("storageLimit", tenant.storageLimit || undefined);
    }
  }, [tenant, setValue]);

  const watchedType = watch("type");

  const onSubmit = async (data: TenantUpdateFormData) => {
    try {
      const result = await updateMutation.mutateAsync({
        tenantId,
        ...data,
        type: data.type as any,
        website: data.website || undefined,
      });
      onSuccess?.(result as any);
    } catch (error) {
      handleError(error);
    }
  };

  if (loadingTenant) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!tenant) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>租户不存在或加载失败</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" size="sm" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          )}
          <div>
            <h2 className="text-2xl font-bold">编辑租户信息</h2>
            <p className="text-muted-foreground">
              {tenant.displayName || tenant.name}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">租户名称 *</Label>
                  <Input
                    id="name"
                    {...register("name")}
                    placeholder="请输入租户名称"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="displayName">显示名称</Label>
                  <Input
                    id="displayName"
                    {...register("displayName")}
                    placeholder="可选，用于显示的友好名称"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  placeholder="请简要描述您的医疗机构"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">机构类型 *</Label>
                  <Select
                    value={watchedType}
                    onValueChange={(value) =>
                      setValue("type", value as TenantType, {
                        shouldValidate: true,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择机构类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(TenantTypeLabels).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">医院类型 *</Label>
                  <Input
                    id="category"
                    {...register("category")}
                    placeholder="如：综合医院、专科医院等"
                  />
                  {errors.category && (
                    <p className="text-sm text-red-500">
                      {errors.category.message}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 联系信息 */}
          <Card>
            <CardHeader>
              <CardTitle>联系信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactName">联系人姓名 *</Label>
                  <Input
                    id="contactName"
                    {...register("contactName")}
                    placeholder="请输入联系人姓名"
                  />
                  {errors.contactName && (
                    <p className="text-sm text-red-500">
                      {errors.contactName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <Input
                    id="contactPhone"
                    {...register("contactPhone")}
                    placeholder="请输入联系电话"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactEmail">联系邮箱 *</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  {...register("contactEmail")}
                  placeholder="请输入联系邮箱"
                />
                {errors.contactEmail && (
                  <p className="text-sm text-red-500">
                    {errors.contactEmail.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">地址</Label>
                <Textarea
                  id="address"
                  {...register("address")}
                  placeholder="请输入详细地址"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">官方网站</Label>
                <Input
                  id="website"
                  type="url"
                  {...register("website")}
                  placeholder="https://example.com"
                />
                {errors.website && (
                  <p className="text-sm text-red-500">
                    {errors.website.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 认证信息和配额设置 */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>认证信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="licenseNumber">医疗机构执业许可证号</Label>
                <Input
                  id="licenseNumber"
                  {...register("licenseNumber")}
                  placeholder="请输入执业许可证号"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="taxId">税务登记号</Label>
                <Input
                  id="taxId"
                  {...register("taxId")}
                  placeholder="请输入税务登记号"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="legalPerson">法人代表</Label>
                <Input
                  id="legalPerson"
                  {...register("legalPerson")}
                  placeholder="请输入法人代表姓名"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>配额设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxUsers">最大用户数</Label>
                  <Input
                    id="maxUsers"
                    type="number"
                    {...register("maxUsers", { valueAsNumber: true })}
                    placeholder="留空表示无限制"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxProjects">最大项目数</Label>
                  <Input
                    id="maxProjects"
                    type="number"
                    {...register("maxProjects", { valueAsNumber: true })}
                    placeholder="留空表示无限制"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxApplications">最大应用数</Label>
                  <Input
                    id="maxApplications"
                    type="number"
                    {...register("maxApplications", { valueAsNumber: true })}
                    placeholder="留空表示无限制"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="storageLimit">存储限制 (GB)</Label>
                  <Input
                    id="storageLimit"
                    type="number"
                    {...register("storageLimit", { valueAsNumber: true })}
                    placeholder="留空表示无限制"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 错误提示 */}
        {updateMutation.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {getErrorMessage(updateMutation.error)}
            </AlertDescription>
          </Alert>
        )}

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-4">
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              取消
            </Button>
          )}
          <Button
            type="submit"
            disabled={!isValid || !isDirty || updateMutation.isPending}
          >
            {updateMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </form>
    </div>
  );
};
