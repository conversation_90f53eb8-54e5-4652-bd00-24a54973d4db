import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { TenantType } from "../types";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import {
  useTenantRegister,
  useTenantErrorHandler,
} from "../hooks/useTenantManagement";

// 表单验证模式
const tenantRegistrationSchema = z.object({
  name: z.string().min(1, "租户名称不能为空"),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(TenantType),
  category: z.string().min(1, "医院类型不能为空"),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url("请输入有效的网址").optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
});

type TenantRegistrationFormData = z.infer<typeof tenantRegistrationSchema>;

interface TenantRegistrationFormProps {
  onSuccess?: (result: any) => void;
  onCancel?: () => void;
}

// 租户类型选项
const tenantTypeOptions = [
  { value: TenantType.HOSPITAL_TERTIARY, label: "三甲医院" },
  { value: TenantType.HOSPITAL_SECONDARY, label: "二甲医院" },
  { value: TenantType.HOSPITAL_PRIMARY, label: "一甲医院" },
  { value: TenantType.HOSPITAL_SPECIALIZED, label: "专科医院" },
  { value: TenantType.CLINIC, label: "诊所" },
  { value: TenantType.HEALTH_CENTER, label: "卫生院" },
  { value: TenantType.MEDICAL_GROUP, label: "医疗集团" },
  { value: TenantType.OTHER, label: "其他" },
];

// 医院类型选项
const categoryOptions = [
  "综合医院",
  "专科医院",
  "中医医院",
  "妇幼保健院",
  "精神病医院",
  "传染病医院",
  "肿瘤医院",
  "心血管医院",
  "眼科医院",
  "口腔医院",
  "康复医院",
  "护理院",
  "社区卫生服务中心",
  "乡镇卫生院",
  "村卫生室",
  "门诊部",
  "诊所",
  "医务室",
  "其他",
];

export const TenantRegistrationForm: React.FC<TenantRegistrationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [step, setStep] = useState(1);
  const [stepError, setStepError] = useState<string | null>(null);
  const registerMutation = useTenantRegister();
  const { handleError, getErrorMessage } = useTenantErrorHandler();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<TenantRegistrationFormData>({
    resolver: zodResolver(tenantRegistrationSchema),
    mode: "onChange",
  });

  const onSubmit = async (data: TenantRegistrationFormData) => {
    try {
      const result = await registerMutation.mutateAsync({
        ...data,
        type: data.type as any,
        website: data.website || undefined,
      });
      onSuccess?.(result);
    } catch (error) {
      handleError(error);
    }
  };

  const nextStep = async () => {
    setStepError(null);

    // 验证当前步骤的必填字段
    let isStepValid = false;
    let errorMessage = "";

    if (step === 1) {
      // 第一步：基本信息验证
      const name = watch("name");
      const type = watch("type");
      const category = watch("category");

      if (!name) errorMessage = "请输入租户名称";
      else if (!type) errorMessage = "请选择机构类型";
      else if (!category) errorMessage = "请输入医院类型";
      else isStepValid = true;
    } else if (step === 2) {
      // 第二步：联系信息验证
      const contactName = watch("contactName");
      const contactEmail = watch("contactEmail");

      if (!contactName) errorMessage = "请输入联系人姓名";
      else if (!contactEmail) errorMessage = "请输入联系邮箱";
      else isStepValid = true;
    }

    if (isStepValid && step < 3) {
      setStep(step + 1);
    } else if (!isStepValid) {
      setStepError(errorMessage);
    }
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  if (registerMutation.isSuccess) {
    return (
      <Card className="mx-auto w-full max-w-2xl">
        <CardContent className="pt-6">
          <div className="space-y-4 text-center">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
            <h3 className="text-lg font-semibold">注册申请已提交</h3>
            <p className="text-muted-foreground">
              您的租户注册申请已成功提交，我们将在1-3个工作日内完成审核。
              审核结果将通过邮件通知您。
            </p>
            <Button onClick={() => window.location.reload()}>
              继续注册其他租户
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader>
        <CardTitle>租户注册申请</CardTitle>
        <CardDescription>
          请填写完整的租户信息，我们将在审核通过后为您开通服务
        </CardDescription>
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>步骤 {step} / 3</span>
          <span>
            {step === 1 && "基本信息"}
            {step === 2 && "联系信息"}
            {step === 3 && "认证信息"}
          </span>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 步骤 1: 基本信息 */}
          {step === 1 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">租户名称 *</Label>
                  <Input
                    id="name"
                    {...register("name")}
                    placeholder="请输入租户名称"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="displayName">显示名称</Label>
                  <Input
                    id="displayName"
                    {...register("displayName")}
                    placeholder="可选，用于显示的友好名称"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  placeholder="请简要描述您的医疗机构"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">机构类型 *</Label>
                  <Select
                    onValueChange={(value) =>
                      setValue("type", value as TenantType)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择机构类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {tenantTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-sm text-red-500">
                      {errors.type.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">医院类型 *</Label>
                  <Select
                    onValueChange={(value) => setValue("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择医院类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-sm text-red-500">
                      {errors.category.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 步骤 2: 联系信息 */}
          {step === 2 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactName">联系人姓名 *</Label>
                  <Input
                    id="contactName"
                    {...register("contactName")}
                    placeholder="请输入联系人姓名"
                  />
                  {errors.contactName && (
                    <p className="text-sm text-red-500">
                      {errors.contactName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <Input
                    id="contactPhone"
                    {...register("contactPhone")}
                    placeholder="请输入联系电话"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactEmail">联系邮箱 *</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  {...register("contactEmail")}
                  placeholder="请输入联系邮箱"
                />
                {errors.contactEmail && (
                  <p className="text-sm text-red-500">
                    {errors.contactEmail.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">地址</Label>
                <Textarea
                  id="address"
                  {...register("address")}
                  placeholder="请输入详细地址"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">官方网站</Label>
                <Input
                  id="website"
                  type="url"
                  {...register("website")}
                  placeholder="https://example.com"
                />
                {errors.website && (
                  <p className="text-sm text-red-500">
                    {errors.website.message}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* 步骤 3: 认证信息 */}
          {step === 3 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="licenseNumber">医疗机构执业许可证号</Label>
                <Input
                  id="licenseNumber"
                  {...register("licenseNumber")}
                  placeholder="请输入执业许可证号"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="taxId">税务登记号</Label>
                <Input
                  id="taxId"
                  {...register("taxId")}
                  placeholder="请输入税务登记号"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="legalPerson">法人代表</Label>
                <Input
                  id="legalPerson"
                  {...register("legalPerson")}
                  placeholder="请输入法人代表姓名"
                />
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  认证信息用于验证您的医疗机构资质，请确保信息准确无误。
                  我们将严格保护您的信息安全。
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* 错误提示 */}
          {stepError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{stepError}</AlertDescription>
            </Alert>
          )}

          {registerMutation.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {getErrorMessage(registerMutation.error)}
              </AlertDescription>
            </Alert>
          )}

          {/* 按钮组 */}
          <div className="flex justify-between">
            <div>
              {step > 1 && (
                <Button type="button" variant="outline" onClick={prevStep}>
                  上一步
                </Button>
              )}
            </div>

            <div className="space-x-2">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  取消
                </Button>
              )}

              {step < 3 ? (
                <Button type="button" onClick={nextStep}>
                  下一步
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={!isValid || registerMutation.isPending}
                >
                  {registerMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  提交申请
                </Button>
              )}
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
