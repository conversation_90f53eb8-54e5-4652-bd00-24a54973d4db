import React, { useState } from "react";
import { TenantType, TenantStatus, type Tenant } from "../types";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/src/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  Loader2,
  Search,
  MoreH<PERSON>zontal,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
} from "lucide-react";
import {
  useTenantList,
  useTenantUpdateStatus,
  useTenantDelete,
  useTenantErrorHandler,
} from "../hooks/useTenantManagement";

interface TenantManagementListProps {
  onViewTenant?: (tenantId: string) => void;
  onEditTenant?: (tenantId: string) => void;
}

// 状态颜色映射
const statusColors: Record<TenantStatus, string> = {
  [TenantStatus.PENDING]: "bg-yellow-100 text-yellow-800",
  [TenantStatus.ACTIVE]: "bg-green-100 text-green-800",
  [TenantStatus.INACTIVE]: "bg-gray-100 text-gray-800",
  [TenantStatus.SUSPENDED]: "bg-red-100 text-red-800",
  [TenantStatus.REJECTED]: "bg-red-100 text-red-800",
  [TenantStatus.EXPIRED]: "bg-orange-100 text-orange-800",
};

// 状态图标映射
const statusIcons: Record<TenantStatus, React.ReactNode> = {
  [TenantStatus.PENDING]: <Clock className="h-3 w-3" />,
  [TenantStatus.ACTIVE]: <CheckCircle className="h-3 w-3" />,
  [TenantStatus.INACTIVE]: <XCircle className="h-3 w-3" />,
  [TenantStatus.SUSPENDED]: <AlertCircle className="h-3 w-3" />,
  [TenantStatus.REJECTED]: <XCircle className="h-3 w-3" />,
  [TenantStatus.EXPIRED]: <AlertCircle className="h-3 w-3" />,
};

// 状态标签映射
const statusLabels: Record<TenantStatus, string> = {
  [TenantStatus.PENDING]: "待审核",
  [TenantStatus.ACTIVE]: "活跃",
  [TenantStatus.INACTIVE]: "已删除",
  [TenantStatus.SUSPENDED]: "暂停",
  [TenantStatus.REJECTED]: "拒绝",
  [TenantStatus.EXPIRED]: "过期",
};

// 租户类型标签映射
const typeLabels: Record<TenantType, string> = {
  [TenantType.HOSPITAL_TERTIARY]: "三甲医院",
  [TenantType.HOSPITAL_SECONDARY]: "二甲医院",
  [TenantType.HOSPITAL_PRIMARY]: "一甲医院",
  [TenantType.HOSPITAL_SPECIALIZED]: "专科医院",
  [TenantType.CLINIC]: "诊所",
  [TenantType.HEALTH_CENTER]: "卫生院",
  [TenantType.MEDICAL_GROUP]: "医疗集团",
  [TenantType.OTHER]: "其他",
};

export const TenantManagementList: React.FC<TenantManagementListProps> = ({
  onViewTenant,
  onEditTenant,
}) => {
  const [filters, setFilters] = useState({
    status: undefined as TenantStatus | undefined,
    type: undefined as TenantType | undefined,
    category: "",
    search: "",
    page: 0,
    limit: 20,
  });

  const { data, isLoading, error } = useTenantList(filters);
  const updateStatusMutation = useTenantUpdateStatus();
  const deleteMutation = useTenantDelete();
  const { getErrorMessage } = useTenantErrorHandler();

  const handleStatusUpdate = async (
    tenantId: string,
    status: TenantStatus,
    reason?: string,
  ) => {
    try {
      await updateStatusMutation.mutateAsync({
        tenantId,
        status: status as any,
        reason,
      });
    } catch (error) {
      // 错误处理已在 hook 中处理
      console.error("Status update failed:", error);
    }
  };

  const handleDelete = async (tenantId: string, reason?: string) => {
    if (!confirm("确定要删除这个租户吗？此操作不可恢复。")) {
      return;
    }

    try {
      await deleteMutation.mutateAsync({ tenantId, reason });
    } catch (error) {
      // 错误处理已在 hook 中处理，这里不需要额外处理
      console.error("Delete operation failed:", error);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 0, // 重置页码
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{getErrorMessage(error)}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>租户管理</CardTitle>
        <CardDescription>
          管理系统中的所有租户，包括审核、状态更新和删除操作
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* 过滤器 */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Label htmlFor="search">搜索</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="搜索租户名称、联系人或邮箱..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="w-48">
              <Label htmlFor="status">状态</Label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) =>
                  handleFilterChange(
                    "status",
                    value === "all" ? undefined : value,
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="所有状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  {Object.entries(statusLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-48">
              <Label htmlFor="type">类型</Label>
              <Select
                value={filters.type || "all"}
                onValueChange={(value) =>
                  handleFilterChange(
                    "type",
                    value === "all" ? undefined : value,
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="所有类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  {Object.entries(typeLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 租户列表 */}
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>租户信息</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>联系人</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.tenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {tenant.displayName || tenant.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {tenant.category}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {typeLabels[tenant.type as keyof typeof typeLabels]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm">{tenant.contactName}</div>
                        <div className="text-xs text-muted-foreground">
                          {tenant.contactEmail}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={
                          statusColors[
                            tenant.status as keyof typeof statusColors
                          ]
                        }
                      >
                        {statusIcons[tenant.status as keyof typeof statusIcons]}
                        <span className="ml-1">
                          {
                            statusLabels[
                              tenant.status as keyof typeof statusLabels
                            ]
                          }
                        </span>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(tenant.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => onViewTenant?.(tenant.id)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onEditTenant?.(tenant.id)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            编辑信息
                          </DropdownMenuItem>

                          {tenant.status === "PENDING" && (
                            <>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusUpdate(
                                    tenant.id,
                                    TenantStatus.ACTIVE,
                                  )
                                }
                              >
                                <CheckCircle className="mr-2 h-4 w-4" />
                                批准
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleStatusUpdate(
                                    tenant.id,
                                    TenantStatus.REJECTED,
                                  )
                                }
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                拒绝
                              </DropdownMenuItem>
                            </>
                          )}

                          {tenant.status === "ACTIVE" && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleStatusUpdate(
                                  tenant.id,
                                  TenantStatus.SUSPENDED,
                                )
                              }
                            >
                              <AlertCircle className="mr-2 h-4 w-4" />
                              暂停
                            </DropdownMenuItem>
                          )}

                          {tenant.status === "SUSPENDED" && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleStatusUpdate(
                                  tenant.id,
                                  TenantStatus.ACTIVE,
                                )
                              }
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              恢复
                            </DropdownMenuItem>
                          )}

                          <DropdownMenuItem
                            onClick={(e) => {
                              console.log("Delete button clicked!", tenant.id);
                              e.preventDefault();
                              e.stopPropagation();
                              handleDelete(tenant.id);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 分页 */}
            {data && data.totalPages > 1 && (
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  共 {data.totalCount} 个租户，第 {data.currentPage + 1} /{" "}
                  {data.totalPages} 页
                </div>
                <div className="space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(data.currentPage - 1)}
                    disabled={data.currentPage === 0}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(data.currentPage + 1)}
                    disabled={data.currentPage >= data.totalPages - 1}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};
