import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { TRPCError } from "@trpc/server";
import {
  ApplicationType,
  ApplicationRequestStatus,
  ApprovalStepType,
  ApprovalStatus,
  ApprovalDecision,
  type PrismaClient,
} from "@langfuse/shared/src/db";

// 输入验证模式
const CreateTenantApplicationSchema = z.object({
  tenantId: z.string(),
  applicationName: z.string().min(1, "应用名称不能为空"),
  applicationType: z.nativeEnum(ApplicationType),
  description: z.string().optional(),
  businessCase: z.string().optional(),
  expectedUsers: z.number().min(1).optional(),
  applicantName: z.string().min(1, "申请人姓名不能为空"),
  applicantEmail: z.string().email("请输入有效的邮箱地址"),
  applicantPhone: z.string().optional(),
  attachments: z
    .array(
      z.object({
        fileName: z.string(),
        fileUrl: z.string(),
        fileSize: z.number(),
        fileType: z.string(),
      }),
    )
    .optional(),
});

const UpdateTenantApplicationSchema = z.object({
  applicationId: z.string(),
  applicationName: z.string().optional(),
  description: z.string().optional(),
  businessCase: z.string().optional(),
  expectedUsers: z.number().min(1).optional(),
  applicantName: z.string().optional(),
  applicantEmail: z.string().email().optional(),
  applicantPhone: z.string().optional(),
  attachments: z
    .array(
      z.object({
        fileName: z.string(),
        fileUrl: z.string(),
        fileSize: z.number(),
        fileType: z.string(),
      }),
    )
    .optional(),
});

const TenantApplicationFilterSchema = z.object({
  tenantId: z.string().optional(),
  status: z.nativeEnum(ApplicationRequestStatus).optional(),
  applicationType: z.nativeEnum(ApplicationType).optional(),
  search: z.string().optional(),
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

const SubmitApplicationSchema = z.object({
  applicationId: z.string(),
});

const WithdrawApplicationSchema = z.object({
  applicationId: z.string(),
  reason: z.string().optional(),
});

// 辅助函数
async function checkTenantApplicationAccess(
  prisma: PrismaClient,
  userId: string,
  applicationId: string,
): Promise<boolean> {
  const application = await prisma.tenantApplication.findUnique({
    where: { id: applicationId },
    include: {
      tenant: {
        include: {
          organizations: {
            include: {
              organization: {
                include: {
                  organizationMemberships: {
                    where: { userId },
                  },
                },
              },
            },
          },
        },
      },
    },
  });

  return !!application?.tenant.organizations.some(
    (to) => to.organization.organizationMemberships.length > 0,
  );
}

export const tenantApplicationRouter = createTRPCRouter({
  // 创建租户应用申请
  create: protectedProcedure
    .input(CreateTenantApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查租户访问权限
      const tenant = await ctx.prisma.tenant.findUnique({
        where: { id: input.tenantId },
        include: {
          organizations: {
            include: {
              organization: {
                include: {
                  organizationMemberships: {
                    where: { userId: ctx.session.user.id },
                  },
                },
              },
            },
          },
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      const hasAccess =
        ctx.session.user.admin ||
        tenant.organizations.some(
          (to) => to.organization.organizationMemberships.length > 0,
        );

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权为该租户创建应用申请",
        });
      }

      // 创建应用申请
      const application = await ctx.prisma.tenantApplication.create({
        data: {
          tenantId: input.tenantId,
          applicationName: input.applicationName,
          applicationType: input.applicationType,
          description: input.description,
          businessCase: input.businessCase,
          expectedUsers: input.expectedUsers,
          applicantName: input.applicantName,
          applicantEmail: input.applicantEmail,
          applicantPhone: input.applicantPhone,
          attachments: input.attachments,
          status: ApplicationRequestStatus.DRAFT,
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: input.tenantId,
          action: "CREATE",
          resourceType: "APPLICATION",
          resourceId: application.id,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            applicationName: application.applicationName,
            applicationType: application.applicationType,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return application;
    }),

  // 获取租户应用申请列表
  list: protectedProcedure
    .input(TenantApplicationFilterSchema)
    .query(async ({ input, ctx }) => {
      const where: any = {};

      // 如果指定了租户ID，检查访问权限
      if (input.tenantId) {
        const tenant = await ctx.prisma.tenant.findUnique({
          where: { id: input.tenantId },
          include: {
            organizations: {
              include: {
                organization: {
                  include: {
                    organizationMemberships: {
                      where: { userId: ctx.session.user.id },
                    },
                  },
                },
              },
            },
          },
        });

        if (!tenant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "租户不存在",
          });
        }

        const hasAccess =
          ctx.session.user.admin ||
          tenant.organizations.some(
            (to) => to.organization.organizationMemberships.length > 0,
          );

        if (!hasAccess) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "无权访问该租户的应用申请",
          });
        }

        where.tenantId = input.tenantId;
      } else if (!ctx.session.user.admin) {
        // 非管理员只能看到自己有权限的租户的申请
        const userTenants = await ctx.prisma.tenant.findMany({
          where: {
            organizations: {
              some: {
                organization: {
                  organizationMemberships: {
                    some: { userId: ctx.session.user.id },
                  },
                },
              },
            },
          },
          select: { id: true },
        });

        where.tenantId = {
          in: userTenants.map((t) => t.id),
        };
      }

      if (input.status) {
        where.status = input.status;
      }

      if (input.applicationType) {
        where.applicationType = input.applicationType;
      }

      if (input.search) {
        where.OR = [
          { applicationName: { contains: input.search, mode: "insensitive" } },
          { description: { contains: input.search, mode: "insensitive" } },
          { applicantName: { contains: input.search, mode: "insensitive" } },
          { applicantEmail: { contains: input.search, mode: "insensitive" } },
        ];
      }

      const [applications, totalCount] = await Promise.all([
        ctx.prisma.tenantApplication.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
                displayName: true,
                type: true,
              },
            },
            approvalWorkflows: {
              orderBy: { stepOrder: "asc" },
              select: {
                id: true,
                stepName: true,
                status: true,
                decision: true,
              },
            },
          },
        }),
        ctx.prisma.tenantApplication.count({ where }),
      ]);

      return {
        applications,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),

  // 获取单个应用申请详情
  byId: protectedProcedure
    .input(z.object({ applicationId: z.string() }))
    .query(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantApplicationAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.applicationId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权访问该应用申请",
        });
      }

      const application = await ctx.prisma.tenantApplication.findUnique({
        where: { id: input.applicationId },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              displayName: true,
              type: true,
              category: true,
              contactName: true,
              contactEmail: true,
            },
          },
          approvalWorkflows: {
            orderBy: { stepOrder: "asc" },
          },
        },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用申请不存在",
        });
      }

      return application;
    }),

  // 更新应用申请
  update: protectedProcedure
    .input(UpdateTenantApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      const { applicationId, ...updateData } = input;

      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantApplicationAccess(
          ctx.prisma,
          ctx.session.user.id,
          applicationId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权修改该应用申请",
        });
      }

      // 获取现有申请信息
      const existingApplication = await ctx.prisma.tenantApplication.findUnique(
        {
          where: { id: applicationId },
        },
      );

      if (!existingApplication) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用申请不存在",
        });
      }

      // 只有草稿状态的申请可以修改
      if (existingApplication.status !== ApplicationRequestStatus.DRAFT) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "只有草稿状态的申请可以修改",
        });
      }

      // 更新申请信息
      const updatedApplication = await ctx.prisma.tenantApplication.update({
        where: { id: applicationId },
        data: updateData,
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: existingApplication.tenantId,
          action: "UPDATE",
          resourceType: "APPLICATION",
          resourceId: applicationId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            before: existingApplication,
            after: updatedApplication,
            changes: updateData,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedApplication;
    }),

  // 提交应用申请
  submit: protectedProcedure
    .input(SubmitApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantApplicationAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.applicationId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权提交该应用申请",
        });
      }

      const application = await ctx.prisma.tenantApplication.findUnique({
        where: { id: input.applicationId },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用申请不存在",
        });
      }

      // 只有草稿状态的申请可以提交
      if (application.status !== ApplicationRequestStatus.DRAFT) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "只有草稿状态的申请可以提交",
        });
      }

      // 验证必填字段
      if (
        !application.applicationName ||
        !application.applicantName ||
        !application.applicantEmail
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "请填写完整的申请信息",
        });
      }

      // 更新申请状态并设置提交时间
      const updatedApplication = await ctx.prisma.tenantApplication.update({
        where: { id: input.applicationId },
        data: {
          status: ApplicationRequestStatus.SUBMITTED,
          submittedAt: new Date(),
        },
      });

      // 创建默认审批工作流
      await ctx.prisma.tenantApprovalWorkflow.createMany({
        data: [
          {
            tenantApplicationId: input.applicationId,
            stepOrder: 1,
            stepName: "初审",
            stepType: ApprovalStepType.MANUAL,
            assigneeRole: "ADMIN",
            status: ApprovalStatus.PENDING,
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后到期
          },
          {
            tenantApplicationId: input.applicationId,
            stepOrder: 2,
            stepName: "终审",
            stepType: ApprovalStepType.MANUAL,
            assigneeRole: "OWNER",
            status: ApprovalStatus.PENDING,
            dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后到期
          },
        ],
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: application.tenantId,
          action: "SUBMIT",
          resourceType: "APPLICATION",
          resourceId: input.applicationId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            applicationName: application.applicationName,
            applicationType: application.applicationType,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedApplication;
    }),

  // 撤回应用申请
  withdraw: protectedProcedure
    .input(WithdrawApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantApplicationAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.applicationId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权撤回该应用申请",
        });
      }

      const application = await ctx.prisma.tenantApplication.findUnique({
        where: { id: input.applicationId },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用申请不存在",
        });
      }

      // 只有已提交或审核中的申请可以撤回
      if (
        ![
          ApplicationRequestStatus.SUBMITTED,
          ApplicationRequestStatus.REVIEWING,
        ].includes(application.status as any)
      ) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "只有已提交或审核中的申请可以撤回",
        });
      }

      // 更新申请状态
      const updatedApplication = await ctx.prisma.tenantApplication.update({
        where: { id: input.applicationId },
        data: {
          status: ApplicationRequestStatus.WITHDRAWN,
        },
      });

      // 取消所有待处理的审批步骤
      await ctx.prisma.tenantApprovalWorkflow.updateMany({
        where: {
          tenantApplicationId: input.applicationId,
          status: {
            in: [ApprovalStatus.PENDING, ApprovalStatus.IN_PROGRESS],
          },
        },
        data: {
          status: ApprovalStatus.SKIPPED,
          completedAt: new Date(),
          comments: "申请已被撤回",
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: application.tenantId,
          action: "WITHDRAW",
          resourceType: "APPLICATION",
          resourceId: input.applicationId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            reason: input.reason,
            applicationName: application.applicationName,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedApplication;
    }),

  // 删除应用申请
  delete: protectedProcedure
    .input(z.object({ applicationId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantApplicationAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.applicationId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权删除该应用申请",
        });
      }

      const application = await ctx.prisma.tenantApplication.findUnique({
        where: { id: input.applicationId },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用申请不存在",
        });
      }

      // 只有草稿、已撤回或已拒绝的申请可以删除
      if (
        ![
          ApplicationRequestStatus.DRAFT,
          ApplicationRequestStatus.WITHDRAWN,
          ApplicationRequestStatus.REJECTED,
        ].includes(application.status as any)
      ) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "只有草稿、已撤回或已拒绝的申请可以删除",
        });
      }

      // 删除申请（级联删除相关的审批工作流）
      await ctx.prisma.tenantApplication.delete({
        where: { id: input.applicationId },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: application.tenantId,
          action: "DELETE",
          resourceType: "APPLICATION",
          resourceId: input.applicationId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            applicationName: application.applicationName,
            applicationType: application.applicationType,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return { success: true, message: "应用申请已删除" };
    }),

  // 获取应用申请统计信息
  stats: protectedProcedure
    .input(z.object({ tenantId: z.string().optional() }))
    .query(async ({ input, ctx }) => {
      const where: any = {};

      if (input.tenantId) {
        // 检查租户访问权限
        const tenant = await ctx.prisma.tenant.findUnique({
          where: { id: input.tenantId },
          include: {
            organizations: {
              include: {
                organization: {
                  include: {
                    organizationMemberships: {
                      where: { userId: ctx.session.user.id },
                    },
                  },
                },
              },
            },
          },
        });

        if (!tenant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "租户不存在",
          });
        }

        const hasAccess =
          ctx.session.user.admin ||
          tenant.organizations.some(
            (to) => to.organization.organizationMemberships.length > 0,
          );

        if (!hasAccess) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "无权访问该租户的统计信息",
          });
        }

        where.tenantId = input.tenantId;
      } else if (!ctx.session.user.admin) {
        // 非管理员只能看到自己有权限的租户的统计
        const userTenants = await ctx.prisma.tenant.findMany({
          where: {
            organizations: {
              some: {
                organization: {
                  organizationMemberships: {
                    some: { userId: ctx.session.user.id },
                  },
                },
              },
            },
          },
          select: { id: true },
        });

        where.tenantId = {
          in: userTenants.map((t) => t.id),
        };
      }

      const [
        totalApplications,
        draftApplications,
        submittedApplications,
        reviewingApplications,
        approvedApplications,
        rejectedApplications,
      ] = await Promise.all([
        ctx.prisma.tenantApplication.count({ where }),
        ctx.prisma.tenantApplication.count({
          where: { ...where, status: ApplicationRequestStatus.DRAFT },
        }),
        ctx.prisma.tenantApplication.count({
          where: { ...where, status: ApplicationRequestStatus.SUBMITTED },
        }),
        ctx.prisma.tenantApplication.count({
          where: { ...where, status: ApplicationRequestStatus.REVIEWING },
        }),
        ctx.prisma.tenantApplication.count({
          where: { ...where, status: ApplicationRequestStatus.APPROVED },
        }),
        ctx.prisma.tenantApplication.count({
          where: { ...where, status: ApplicationRequestStatus.REJECTED },
        }),
      ]);

      return {
        totalApplications,
        draftApplications,
        submittedApplications,
        reviewingApplications,
        approvedApplications,
        rejectedApplications,
      };
    }),
});
