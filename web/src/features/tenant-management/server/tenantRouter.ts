import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { TRPCError } from "@trpc/server";
import { auditLog } from "@/src/features/audit-logs/auditLog";
import {
  TenantType,
  TenantStatus,
  TenantRole,
  type PrismaClient,
} from "@langfuse/shared/src/db";
import { randomBytes } from "crypto";

// 输入验证模式
const CreateTenantSchema = z.object({
  name: z.string().min(1, "租户名称不能为空"),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(TenantType),
  category: z.string().min(1, "医院类型不能为空"),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
  settings: z.record(z.string(), z.any()).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

const UpdateTenantSchema = z.object({
  tenantId: z.string(),
  name: z.string().min(1).optional(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(TenantType).optional(),
  category: z.string().optional(),
  contactName: z.string().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
  settings: z.record(z.string(), z.any()).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

const TenantFilterSchema = z.object({
  status: z.nativeEnum(TenantStatus).optional(),
  type: z.nativeEnum(TenantType).optional(),
  category: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

const UpdateTenantStatusSchema = z.object({
  tenantId: z.string(),
  status: z.nativeEnum(TenantStatus),
  reason: z.string().optional(),
});

// 辅助函数
async function checkTenantAccess(
  prisma: PrismaClient,
  userId: string,
  tenantId: string,
  requiredRole: TenantRole = TenantRole.MEMBER,
): Promise<boolean> {
  const tenantOrg = await prisma.tenantOrganization.findFirst({
    where: {
      tenantId,
      organization: {
        organizationMemberships: {
          some: {
            userId,
            role: {
              in: ["OWNER", "ADMIN", "MEMBER"], // 映射到现有的Role枚举
            },
          },
        },
      },
    },
    include: {
      organization: {
        include: {
          organizationMemberships: {
            where: { userId },
          },
        },
      },
    },
  });

  return !!tenantOrg;
}

export const tenantRouter = createTRPCRouter({
  // 公开的租户注册接口
  register: publicProcedure
    .input(CreateTenantSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查租户名称是否已存在（只检查活跃状态的租户）
      const existingTenant = await ctx.prisma.tenant.findFirst({
        where: {
          AND: [
            {
              OR: [
                { name: input.name },
                { licenseNumber: input.licenseNumber },
                { taxId: input.taxId },
              ].filter(Boolean),
            },
            {
              // 只检查非删除状态的租户
              status: {
                not: TenantStatus.INACTIVE,
              },
            },
          ],
        },
      });

      if (existingTenant) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "租户名称、许可证号或税务登记号已存在",
        });
      }

      // 创建租户
      const tenant = await ctx.prisma.tenant.create({
        data: {
          name: input.name,
          displayName: input.displayName,
          description: input.description,
          type: input.type,
          category: input.category,
          contactName: input.contactName,
          contactEmail: input.contactEmail,
          contactPhone: input.contactPhone,
          address: input.address,
          website: input.website,
          licenseNumber: input.licenseNumber,
          taxId: input.taxId,
          legalPerson: input.legalPerson,
          settings: input.settings as any,
          metadata: input.metadata as any,
          status: TenantStatus.PENDING,
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: tenant.id,
          action: "CREATE",
          resourceType: "TENANT",
          resourceId: tenant.id,
          userEmail: input.contactEmail,
          details: {
            tenantName: tenant.name,
            tenantType: tenant.type,
          },
          ipAddress:
            (ctx.headers["x-forwarded-for"] as string) ||
            (ctx.headers["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers["user-agent"] as string,
        },
      });

      return {
        id: tenant.id,
        name: tenant.name,
        status: tenant.status,
        message: "租户注册申请已提交，请等待审核",
      };
    }),

  // 获取租户列表（管理员）
  list: protectedProcedure
    .input(TenantFilterSchema)
    .query(async ({ input, ctx }) => {
      // 检查是否为系统管理员
      if (!ctx.session.user.admin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有系统管理员可以查看租户列表",
        });
      }

      const where: any = {};

      // 默认不显示已删除的租户，除非明确筛选INACTIVE状态
      if (input.status) {
        where.status = input.status;
      } else {
        // 如果没有指定状态筛选，则排除已删除的租户
        where.status = {
          not: TenantStatus.INACTIVE,
        };
      }

      if (input.type) {
        where.type = input.type;
      }

      if (input.category) {
        where.category = { contains: input.category, mode: "insensitive" };
      }

      if (input.search) {
        where.OR = [
          { name: { contains: input.search, mode: "insensitive" } },
          { displayName: { contains: input.search, mode: "insensitive" } },
          { contactName: { contains: input.search, mode: "insensitive" } },
          { contactEmail: { contains: input.search, mode: "insensitive" } },
        ];
      }

      const [tenants, totalCount] = await Promise.all([
        ctx.prisma.tenant.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
          include: {
            organizations: {
              include: {
                organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            _count: {
              select: {
                applications: true,
                quotas: true,
              },
            },
          },
        }),
        ctx.prisma.tenant.count({ where }),
      ]);

      return {
        tenants,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),

  // 获取单个租户详情
  byId: protectedProcedure
    .input(z.object({ tenantId: z.string() }))
    .query(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.tenantId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权访问该租户信息",
        });
      }

      const tenant = await ctx.prisma.tenant.findUnique({
        where: { id: input.tenantId },
        include: {
          organizations: {
            include: {
              organization: {
                include: {
                  organizationMemberships: {
                    include: {
                      user: {
                        select: {
                          id: true,
                          name: true,
                          email: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          applications: {
            orderBy: { createdAt: "desc" },
            take: 10,
          },
          quotas: true,
          _count: {
            select: {
              applications: true,
              auditLogs: true,
            },
          },
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      return tenant;
    }),

  // 更新租户信息
  update: protectedProcedure
    .input(UpdateTenantSchema)
    .mutation(async ({ input, ctx }) => {
      const { tenantId, ...updateData } = input;

      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantAccess(
          ctx.prisma,
          ctx.session.user.id,
          tenantId,
          TenantRole.ADMIN,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权修改该租户信息",
        });
      }

      // 获取现有租户信息
      const existingTenant = await ctx.prisma.tenant.findUnique({
        where: { id: tenantId },
      });

      if (!existingTenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      // 检查名称、许可证号、税务登记号是否重复
      if (updateData.name || updateData.licenseNumber || updateData.taxId) {
        const duplicateCheck = await ctx.prisma.tenant.findFirst({
          where: {
            AND: [
              { id: { not: tenantId } },
              {
                OR: [
                  updateData.name ? { name: updateData.name } : {},
                  updateData.licenseNumber
                    ? { licenseNumber: updateData.licenseNumber }
                    : {},
                  updateData.taxId ? { taxId: updateData.taxId } : {},
                ].filter((obj) => Object.keys(obj).length > 0),
              },
            ],
          },
        });

        if (duplicateCheck) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "租户名称、许可证号或税务登记号已存在",
          });
        }
      }

      // 更新租户信息
      const updatedTenant = await ctx.prisma.tenant.update({
        where: { id: tenantId },
        data: updateData as any,
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId,
          action: "UPDATE",
          resourceType: "TENANT",
          resourceId: tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            before: existingTenant,
            after: updatedTenant,
            changes: updateData,
          },
          ipAddress:
            (ctx.headers["x-forwarded-for"] as string) ||
            (ctx.headers["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers["user-agent"] as string,
        },
      });

      return updatedTenant;
    }),

  // 更新租户状态（管理员）
  updateStatus: protectedProcedure
    .input(UpdateTenantStatusSchema)
    .mutation(async ({ input, ctx }) => {
      // 只有系统管理员可以更新租户状态
      if (!ctx.session.user.admin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有系统管理员可以更新租户状态",
        });
      }

      const existingTenant = await ctx.prisma.tenant.findUnique({
        where: { id: input.tenantId },
      });

      if (!existingTenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      const updateData: any = {
        status: input.status,
      };

      // 根据状态设置相关字段
      switch (input.status) {
        case TenantStatus.ACTIVE:
          updateData.isActive = true;
          updateData.isVerified = true;
          updateData.verifiedAt = new Date();
          updateData.suspendedAt = null;
          break;
        case TenantStatus.SUSPENDED:
          updateData.isActive = false;
          updateData.suspendedAt = new Date();
          break;
        case TenantStatus.INACTIVE:
        case TenantStatus.REJECTED:
        case TenantStatus.EXPIRED:
          updateData.isActive = false;
          break;
        default:
          break;
      }

      const updatedTenant = await ctx.prisma.tenant.update({
        where: { id: input.tenantId },
        data: updateData,
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: input.tenantId,
          action: "UPDATE_STATUS",
          resourceType: "TENANT",
          resourceId: input.tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            previousStatus: existingTenant.status,
            newStatus: input.status,
            reason: input.reason,
          },
          ipAddress:
            (ctx.headers["x-forwarded-for"] as string) ||
            (ctx.headers["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers["user-agent"] as string,
        },
      });

      return updatedTenant;
    }),

  // 删除租户（软删除）
  delete: protectedProcedure
    .input(
      z.object({
        tenantId: z.string(),
        reason: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 只有系统管理员可以删除租户
      if (!ctx.session.user.admin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有系统管理员可以删除租户",
        });
      }

      const tenant = await ctx.prisma.tenant.findUnique({
        where: { id: input.tenantId },
        include: {
          organizations: true,
          applications: true,
        },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      // 检查是否有关联的组织或应用
      if (tenant.organizations.length > 0 || tenant.applications.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "无法删除有关联组织或应用的租户，请先处理相关数据",
        });
      }

      // 软删除：更新状态为INACTIVE，并清空唯一字段以避免约束冲突
      const deletedTenant = await ctx.prisma.tenant.update({
        where: { id: input.tenantId },
        data: {
          status: TenantStatus.INACTIVE,
          isActive: false,
          // 清空唯一字段以允许重新使用相同的许可证号和税务登记号
          licenseNumber: null,
          taxId: null,
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: input.tenantId,
          action: "DELETE",
          resourceType: "TENANT",
          resourceId: input.tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            reason: input.reason,
            tenantName: tenant.name,
          },
          ipAddress:
            (ctx.headers["x-forwarded-for"] as string) ||
            (ctx.headers["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers["user-agent"] as string,
        },
      });

      return { success: true, message: "租户已删除" };
    }),

  // 获取租户统计信息
  stats: protectedProcedure
    .input(z.object({ tenantId: z.string().optional() }))
    .query(async ({ input, ctx }) => {
      if (input.tenantId) {
        // 获取特定租户的统计信息
        const hasAccess =
          ctx.session.user.admin ||
          (await checkTenantAccess(
            ctx.prisma,
            ctx.session.user.id,
            input.tenantId,
          ));

        if (!hasAccess) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "无权访问该租户统计信息",
          });
        }

        const stats = await ctx.prisma.tenant.findUnique({
          where: { id: input.tenantId },
          select: {
            _count: {
              select: {
                organizations: true,
                applications: true,
                quotas: true,
                auditLogs: true,
              },
            },
            quotas: {
              select: {
                quotaType: true,
                limit: true,
                used: true,
                period: true,
              },
            },
          },
        });

        return stats;
      } else {
        // 获取系统级别的租户统计信息（仅管理员）
        if (!ctx.session.user.admin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "只有系统管理员可以查看系统统计信息",
          });
        }

        const [
          totalTenants,
          activeTenants,
          pendingTenants,
          suspendedTenants,
          totalApplications,
        ] = await Promise.all([
          // 只统计非删除状态的租户
          ctx.prisma.tenant.count({
            where: { status: { not: TenantStatus.INACTIVE } },
          }),
          ctx.prisma.tenant.count({ where: { status: TenantStatus.ACTIVE } }),
          ctx.prisma.tenant.count({ where: { status: TenantStatus.PENDING } }),
          ctx.prisma.tenant.count({
            where: { status: TenantStatus.SUSPENDED },
          }),
          ctx.prisma.tenantApplication.count(),
        ]);

        return {
          totalTenants,
          activeTenants,
          pendingTenants,
          suspendedTenants,
          totalApplications,
        };
      }
    }),

  // 获取租户审计日志
  auditLogs: protectedProcedure
    .input(
      z.object({
        tenantId: z.string(),
        page: z.number().min(0).default(0),
        limit: z.number().min(1).max(100).default(20),
        action: z.string().optional(),
        resourceType: z.string().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess =
        ctx.session.user.admin ||
        (await checkTenantAccess(
          ctx.prisma,
          ctx.session.user.id,
          input.tenantId,
        ));

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权访问该租户审计日志",
        });
      }

      const where: any = {
        tenantId: input.tenantId,
      };

      if (input.action) {
        where.action = input.action;
      }

      if (input.resourceType) {
        where.resourceType = input.resourceType;
      }

      const [logs, totalCount] = await Promise.all([
        ctx.prisma.tenantAuditLog.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
        }),
        ctx.prisma.tenantAuditLog.count({ where }),
      ]);

      return {
        logs,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),
});
