import { createTRPCRouter, protectedProcedure } from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { TRPCError } from "@trpc/server";
import {
  ApprovalStatus,
  ApprovalDecision,
  ApplicationRequestStatus,
  type PrismaClient,
} from "@langfuse/shared/src/db";

// 输入验证模式
const ProcessApprovalSchema = z.object({
  workflowId: z.string(),
  decision: z.nativeEnum(ApprovalDecision),
  comments: z.string().optional(),
  attachments: z
    .array(
      z.object({
        fileName: z.string(),
        fileUrl: z.string(),
        fileSize: z.number(),
        fileType: z.string(),
      }),
    )
    .optional(),
});

const ApprovalFilterSchema = z.object({
  status: z.nativeEnum(ApprovalStatus).optional(),
  assigneeId: z.string().optional(),
  assigneeRole: z.string().optional(),
  tenantId: z.string().optional(),
  overdue: z.boolean().optional(),
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

const AssignApprovalSchema = z.object({
  workflowId: z.string(),
  assigneeId: z.string(),
});

const UpdateApprovalDueDateSchema = z.object({
  workflowId: z.string(),
  dueDate: z.date(),
});

// 辅助函数
async function checkApprovalAccess(
  prisma: PrismaClient,
  userId: string,
  workflowId: string,
  isAdmin: boolean = false,
): Promise<boolean> {
  const workflow = await prisma.tenantApprovalWorkflow.findUnique({
    where: { id: workflowId },
    include: {
      tenantApplication: {
        include: {
          tenant: {
            include: {
              organizations: {
                include: {
                  organization: {
                    include: {
                      organizationMemberships: {
                        where: { userId },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  });

  if (!workflow) return false;

  // 系统管理员有全部权限
  if (isAdmin) return true;

  // 检查是否为指定的审批人
  if (workflow.assigneeId === userId) return true;

  // 检查是否有租户访问权限
  const hasAccess = workflow.tenantApplication.tenant.organizations.some(
    (to) => to.organization.organizationMemberships.length > 0,
  );

  return hasAccess;
}

async function updateApplicationStatus(
  prisma: PrismaClient,
  applicationId: string,
): Promise<void> {
  // 获取所有审批步骤的状态
  const workflows = await prisma.tenantApprovalWorkflow.findMany({
    where: { tenantApplicationId: applicationId },
    orderBy: { stepOrder: "asc" },
  });

  let newStatus: any = ApplicationRequestStatus.REVIEWING;

  // 检查是否有被拒绝的步骤
  const hasRejected = workflows.some(
    (w) => w.decision === ApprovalDecision.REJECT,
  );
  if (hasRejected) {
    newStatus = "REJECTED" as any;
  } else {
    // 检查是否所有步骤都已完成且批准
    const allApproved = workflows.every(
      (w) =>
        w.status === ApprovalStatus.COMPLETED &&
        w.decision === ApprovalDecision.APPROVE,
    );
    if (allApproved) {
      newStatus = "APPROVED" as any;
    }
  }

  // 更新申请状态
  const updateData: any = { status: newStatus };

  if (newStatus === "APPROVED") {
    updateData.approvedAt = new Date();
  } else if (newStatus === "REJECTED") {
    updateData.rejectedAt = new Date();
  }

  await prisma.tenantApplication.update({
    where: { id: applicationId },
    data: updateData,
  });
}

export const approvalRouter = createTRPCRouter({
  // 获取待审批列表
  list: protectedProcedure
    .input(ApprovalFilterSchema)
    .query(async ({ input, ctx }) => {
      const where: any = {};

      if (input.status) {
        where.status = input.status;
      }

      if (input.assigneeId) {
        where.assigneeId = input.assigneeId;
      }

      if (input.assigneeRole) {
        where.assigneeRole = input.assigneeRole;
      }

      if (input.overdue) {
        where.dueDate = {
          lt: new Date(),
        };
        where.status = {
          in: [ApprovalStatus.PENDING, ApprovalStatus.IN_PROGRESS],
        };
      }

      // 如果不是管理员，只显示自己的审批任务或有权限的租户的审批
      if (!ctx.session.user.admin) {
        const userTenants = await ctx.prisma.tenant.findMany({
          where: {
            organizations: {
              some: {
                organization: {
                  organizationMemberships: {
                    some: { userId: ctx.session.user.id },
                  },
                },
              },
            },
          },
          select: { id: true },
        });

        where.OR = [
          { assigneeId: ctx.session.user.id },
          {
            tenantApplication: {
              tenantId: {
                in: userTenants.map((t) => t.id),
              },
            },
          },
        ];
      }

      if (input.tenantId) {
        where.tenantApplication = {
          tenantId: input.tenantId,
        };
      }

      const [workflows, totalCount] = await Promise.all([
        ctx.prisma.tenantApprovalWorkflow.findMany({
          where,
          orderBy: [{ dueDate: "asc" }, { createdAt: "desc" }],
          skip: input.page * input.limit,
          take: input.limit,
          include: {
            tenantApplication: {
              include: {
                tenant: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true,
                    type: true,
                  },
                },
              },
            },
          },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({ where }),
      ]);

      return {
        workflows,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),

  // 获取单个审批详情
  byId: protectedProcedure
    .input(z.object({ workflowId: z.string() }))
    .query(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess = await checkApprovalAccess(
        ctx.prisma,
        ctx.session.user.id,
        input.workflowId,
        ctx.session.user.admin,
      );

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权访问该审批任务",
        });
      }

      const workflow = await ctx.prisma.tenantApprovalWorkflow.findUnique({
        where: { id: input.workflowId },
        include: {
          tenantApplication: {
            include: {
              tenant: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  type: true,
                  category: true,
                  contactName: true,
                  contactEmail: true,
                  contactPhone: true,
                  address: true,
                  licenseNumber: true,
                },
              },
              approvalWorkflows: {
                orderBy: { stepOrder: "asc" },
              },
            },
          },
        },
      });

      if (!workflow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "审批任务不存在",
        });
      }

      return workflow;
    }),

  // 处理审批
  process: protectedProcedure
    .input(ProcessApprovalSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查访问权限
      const hasAccess = await checkApprovalAccess(
        ctx.prisma,
        ctx.session.user.id,
        input.workflowId,
        ctx.session.user.admin,
      );

      if (!hasAccess) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权处理该审批任务",
        });
      }

      const workflow = await ctx.prisma.tenantApprovalWorkflow.findUnique({
        where: { id: input.workflowId },
        include: {
          tenantApplication: true,
        },
      });

      if (!workflow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "审批任务不存在",
        });
      }

      // 检查审批状态
      if (
        workflow.status !== ApprovalStatus.PENDING &&
        workflow.status !== ApprovalStatus.IN_PROGRESS
      ) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该审批任务已处理完成",
        });
      }

      // 更新审批工作流
      const updatedWorkflow = await ctx.prisma.tenantApprovalWorkflow.update({
        where: { id: input.workflowId },
        data: {
          status: ApprovalStatus.COMPLETED,
          decision: input.decision,
          comments: input.comments,
          attachments: input.attachments,
          completedAt: new Date(),
          assigneeId: ctx.session.user.id, // 记录实际处理人
        },
      });

      // 如果是批准，启动下一个审批步骤
      if (input.decision === ApprovalDecision.APPROVE) {
        const nextWorkflow = await ctx.prisma.tenantApprovalWorkflow.findFirst({
          where: {
            tenantApplicationId: workflow.tenantApplicationId,
            stepOrder: workflow.stepOrder + 1,
            status: ApprovalStatus.PENDING,
          },
        });

        if (nextWorkflow) {
          await ctx.prisma.tenantApprovalWorkflow.update({
            where: { id: nextWorkflow.id },
            data: {
              status: ApprovalStatus.IN_PROGRESS,
              startedAt: new Date(),
            },
          });
        }
      }

      // 更新申请状态
      await updateApplicationStatus(ctx.prisma, workflow.tenantApplicationId);

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: workflow.tenantApplication.tenantId,
          action:
            input.decision === ApprovalDecision.APPROVE ? "APPROVE" : "REJECT",
          resourceType: "APPLICATION",
          resourceId: workflow.tenantApplicationId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            workflowId: input.workflowId,
            stepName: workflow.stepName,
            decision: input.decision,
            comments: input.comments,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedWorkflow;
    }),

  // 分配审批人
  assign: protectedProcedure
    .input(AssignApprovalSchema)
    .mutation(async ({ input, ctx }) => {
      // 只有管理员可以分配审批人
      if (!ctx.session.user.admin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有管理员可以分配审批人",
        });
      }

      const workflow = await ctx.prisma.tenantApprovalWorkflow.findUnique({
        where: { id: input.workflowId },
        include: {
          tenantApplication: true,
        },
      });

      if (!workflow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "审批任务不存在",
        });
      }

      // 检查审批状态
      if (workflow.status === ApprovalStatus.COMPLETED) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该审批任务已完成，无法重新分配",
        });
      }

      // 检查被分配的用户是否存在
      const assignee = await ctx.prisma.user.findUnique({
        where: { id: input.assigneeId },
      });

      if (!assignee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "指定的审批人不存在",
        });
      }

      // 更新审批人
      const updatedWorkflow = await ctx.prisma.tenantApprovalWorkflow.update({
        where: { id: input.workflowId },
        data: {
          assigneeId: input.assigneeId,
          assigneeRole: null, // 清除角色分配
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: workflow.tenantApplication.tenantId,
          action: "ASSIGN",
          resourceType: "APPROVAL",
          resourceId: input.workflowId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            assigneeId: input.assigneeId,
            assigneeName: assignee.name,
            assigneeEmail: assignee.email,
            stepName: workflow.stepName,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedWorkflow;
    }),

  // 更新审批截止时间
  updateDueDate: protectedProcedure
    .input(UpdateApprovalDueDateSchema)
    .mutation(async ({ input, ctx }) => {
      // 只有管理员可以更新截止时间
      if (!ctx.session.user.admin) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "只有管理员可以更新审批截止时间",
        });
      }

      const workflow = await ctx.prisma.tenantApprovalWorkflow.findUnique({
        where: { id: input.workflowId },
        include: {
          tenantApplication: true,
        },
      });

      if (!workflow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "审批任务不存在",
        });
      }

      // 检查审批状态
      if (workflow.status === ApprovalStatus.COMPLETED) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "该审批任务已完成，无法更新截止时间",
        });
      }

      // 更新截止时间
      const updatedWorkflow = await ctx.prisma.tenantApprovalWorkflow.update({
        where: { id: input.workflowId },
        data: {
          dueDate: input.dueDate,
        },
      });

      // 记录审计日志
      await ctx.prisma.tenantAuditLog.create({
        data: {
          tenantId: workflow.tenantApplication.tenantId,
          action: "UPDATE",
          resourceType: "APPROVAL",
          resourceId: input.workflowId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email,
          details: {
            previousDueDate: workflow.dueDate,
            newDueDate: input.dueDate,
            stepName: workflow.stepName,
          },
          ipAddress:
            (ctx.headers?.["x-forwarded-for"] as string) ||
            (ctx.headers?.["x-real-ip"] as string) ||
            "unknown",
          userAgent: ctx.headers?.["user-agent"],
        },
      });

      return updatedWorkflow;
    }),

  // 获取审批统计信息
  stats: protectedProcedure
    .input(
      z.object({
        tenantId: z.string().optional(),
        assigneeId: z.string().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const where: any = {};

      if (input.tenantId) {
        where.tenantApplication = {
          tenantId: input.tenantId,
        };
      }

      if (input.assigneeId) {
        where.assigneeId = input.assigneeId;
      }

      // 如果不是管理员，只显示自己的统计或有权限的租户的统计
      if (!ctx.session.user.admin) {
        const userTenants = await ctx.prisma.tenant.findMany({
          where: {
            organizations: {
              some: {
                organization: {
                  organizationMemberships: {
                    some: { userId: ctx.session.user.id },
                  },
                },
              },
            },
          },
          select: { id: true },
        });

        if (!input.assigneeId) {
          where.OR = [
            { assigneeId: ctx.session.user.id },
            {
              tenantApplication: {
                tenantId: {
                  in: userTenants.map((t) => t.id),
                },
              },
            },
          ];
        }
      }

      const [
        totalApprovals,
        pendingApprovals,
        inProgressApprovals,
        completedApprovals,
        overdueApprovals,
        approvedCount,
        rejectedCount,
      ] = await Promise.all([
        ctx.prisma.tenantApprovalWorkflow.count({ where }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: { ...where, status: ApprovalStatus.PENDING },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: { ...where, status: ApprovalStatus.IN_PROGRESS },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: { ...where, status: ApprovalStatus.COMPLETED },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: {
            ...where,
            status: {
              in: [ApprovalStatus.PENDING, ApprovalStatus.IN_PROGRESS],
            },
            dueDate: { lt: new Date() },
          },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: {
            ...where,
            status: ApprovalStatus.COMPLETED,
            decision: ApprovalDecision.APPROVE,
          },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({
          where: {
            ...where,
            status: ApprovalStatus.COMPLETED,
            decision: ApprovalDecision.REJECT,
          },
        }),
      ]);

      return {
        totalApprovals,
        pendingApprovals,
        inProgressApprovals,
        completedApprovals,
        overdueApprovals,
        approvedCount,
        rejectedCount,
      };
    }),

  // 获取我的待办审批
  myTasks: protectedProcedure
    .input(
      z.object({
        page: z.number().min(0).default(0),
        limit: z.number().min(1).max(100).default(20),
      }),
    )
    .query(async ({ input, ctx }) => {
      const where = {
        assigneeId: ctx.session.user.id,
        status: {
          in: [ApprovalStatus.PENDING, ApprovalStatus.IN_PROGRESS],
        },
      };

      const [workflows, totalCount] = await Promise.all([
        ctx.prisma.tenantApprovalWorkflow.findMany({
          where,
          orderBy: [{ dueDate: "asc" }, { createdAt: "desc" }],
          skip: input.page * input.limit,
          take: input.limit,
          include: {
            tenantApplication: {
              include: {
                tenant: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true,
                    type: true,
                  },
                },
              },
            },
          },
        }),
        ctx.prisma.tenantApprovalWorkflow.count({ where }),
      ]);

      return {
        workflows,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),
});
