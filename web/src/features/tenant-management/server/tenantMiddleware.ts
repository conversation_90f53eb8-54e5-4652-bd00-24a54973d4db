import { createTRPCRouter, protectedProcedure, t } from "@/src/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod/v4";
import {
  type TenantScope,
  type SystemTenantScope,
} from "@/src/features/tenant-management/constants/tenantAccessRights";
import {
  hasTenantAccess,
  hasSystemTenantAccess,
  getUserTenantRole,
  auditTenantAccess,
} from "@/src/features/tenant-management/utils/checkTenantAccess";

// 租户权限中间件输入验证
const tenantAccessInputSchema = z.object({
  tenantId: z.string(),
});

/**
 * 租户权限检查中间件
 * 确保用户有权限访问指定的租户资源
 */
export const createTenantAccessMiddleware = (scope: TenantScope) => {
  return t.middleware(async (opts) => {
    const { ctx, next } = opts;

    // 检查用户是否已认证
    if (!ctx.session || !ctx.session.user) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }

    // 解析输入参数获取tenantId
    const actualInput = await opts.getRawInput();
    const parsedInput = tenantAccessInputSchema.safeParse(actualInput);

    if (!parsedInput.success) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Invalid input, tenantId is required",
      });
    }

    const { tenantId } = parsedInput.data;

    // 检查租户权限
    const hasAccess = await hasTenantAccess({
      session: ctx.session,
      tenantId,
      scope,
      prisma: ctx.prisma,
    });

    if (!hasAccess) {
      // 记录权限检查失败的审计日志
      await auditTenantAccess(ctx.prisma, {
        userId: ctx.session.user.id,
        tenantId,
        action: scope,
        resource: "TENANT",
        granted: false,
        ipAddress:
          (ctx.headers?.["x-forwarded-for"] as string) ||
          (ctx.headers?.["x-real-ip"] as string) ||
          "unknown",
        userAgent: ctx.headers?.["user-agent"],
      });

      throw new TRPCError({
        code: "FORBIDDEN",
        message: "用户没有权限访问此租户资源",
      });
    }

    // 获取用户在租户中的角色
    const tenantRole = await getUserTenantRole(
      ctx.prisma,
      ctx.session.user.id,
      tenantId,
    );

    // 记录权限检查成功的审计日志
    await auditTenantAccess(ctx.prisma, {
      userId: ctx.session.user.id,
      tenantId,
      action: scope,
      resource: "TENANT",
      granted: true,
      ipAddress:
        (ctx.headers?.["x-forwarded-for"] as string) ||
        (ctx.headers?.["x-real-ip"] as string) ||
        "unknown",
      userAgent: ctx.headers?.["user-agent"],
    });

    return next({
      ctx: {
        ...ctx,
        session: {
          ...ctx.session,
          tenantId,
          tenantRole,
        },
      },
    });
  });
};

/**
 * 系统级租户权限检查中间件
 * 确保用户是系统管理员
 */
export const createSystemTenantAccessMiddleware = (
  scope: SystemTenantScope,
) => {
  return t.middleware(async (opts) => {
    const { ctx, next } = opts;

    // 检查用户是否已认证
    if (!ctx.session || !ctx.session.user) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }

    // 检查系统级权限
    const hasAccess = hasSystemTenantAccess({
      session: ctx.session,
      scope,
    });

    if (!hasAccess) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有系统管理员可以执行此操作",
      });
    }

    return next({
      ctx: {
        ...ctx,
        session: {
          ...ctx.session,
          isSystemAdmin: true,
        },
      },
    });
  });
};

/**
 * 租户成员权限检查中间件
 * 检查用户是否是租户成员（任何角色）
 */
export const tenantMemberMiddleware = t.middleware(async (opts) => {
  const { ctx, next } = opts;

  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const actualInput = await opts.getRawInput();
  const parsedInput = tenantAccessInputSchema.safeParse(actualInput);

  if (!parsedInput.success) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid input, tenantId is required",
    });
  }

  const { tenantId } = parsedInput.data;

  // 检查用户是否是租户成员
  const tenantRole = await getUserTenantRole(
    ctx.prisma,
    ctx.session.user.id,
    tenantId,
  );

  if (!tenantRole && !ctx.session.user.admin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "用户不是此租户的成员",
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        tenantId,
        tenantRole,
      },
    },
  });
});

/**
 * 租户管理员权限检查中间件
 * 检查用户是否是租户管理员或所有者
 */
export const tenantAdminMiddleware = t.middleware(async (opts) => {
  const { ctx, next } = opts;

  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const actualInput = await opts.getRawInput();
  const parsedInput = tenantAccessInputSchema.safeParse(actualInput);

  if (!parsedInput.success) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid input, tenantId is required",
    });
  }

  const { tenantId } = parsedInput.data;

  // 系统管理员有所有权限
  if (ctx.session.user.admin) {
    return next({
      ctx: {
        ...ctx,
        session: {
          ...ctx.session,
          tenantId,
          tenantRole: "OWNER" as const,
        },
      },
    });
  }

  // 检查用户是否是租户管理员或所有者
  const tenantRole = await getUserTenantRole(
    ctx.prisma,
    ctx.session.user.id,
    tenantId,
  );

  if (!tenantRole || !["OWNER", "ADMIN"].includes(tenantRole)) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "需要租户管理员权限",
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        tenantId,
        tenantRole,
      },
    },
  });
});

/**
 * 租户所有者权限检查中间件
 * 检查用户是否是租户所有者
 */
export const tenantOwnerMiddleware = t.middleware(async (opts) => {
  const { ctx, next } = opts;

  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const actualInput = await opts.getRawInput();
  const parsedInput = tenantAccessInputSchema.safeParse(actualInput);

  if (!parsedInput.success) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid input, tenantId is required",
    });
  }

  const { tenantId } = parsedInput.data;

  // 系统管理员有所有权限
  if (ctx.session.user.admin) {
    return next({
      ctx: {
        ...ctx,
        session: {
          ...ctx.session,
          tenantId,
          tenantRole: "OWNER" as const,
        },
      },
    });
  }

  // 检查用户是否是租户所有者
  const tenantRole = await getUserTenantRole(
    ctx.prisma,
    ctx.session.user.id,
    tenantId,
  );

  if (tenantRole !== "OWNER") {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "需要租户所有者权限",
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: {
        ...ctx.session,
        tenantId,
        tenantRole,
      },
    },
  });
});
