/**
 * 租户管理相关的类型定义
 *
 * 这个文件定义了前端使用的类型，避免直接导入服务器端的数据库包
 * 这样可以防止在客户端代码中引入服务器端依赖
 */

// 租户类型枚举 - 与 Prisma 生成的类型保持一致
export enum TenantType {
  HOSPITAL_TERTIARY = "hospital_tertiary",
  HOSPITAL_SECONDARY = "hospital_secondary",
  HOSPITAL_PRIMARY = "hospital_primary",
  HOSPITAL_SPECIALIZED = "hospital_specialized",
  CLINIC = "clinic",
  HEALTH_CENTER = "health_center",
  MEDICAL_GROUP = "medical_group",
  OTHER = "other",
}

// 租户状态枚举 - 与 Prisma 生成的类型保持一致
export enum TenantStatus {
  PENDING = "pending",
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
  REJECTED = "rejected",
  EXPIRED = "expired",
}

// 应用类型枚举
export enum ApplicationType {
  INTELLIGENT_AGENT_APP = "INTELLIGENT_AGENT_APP",
  CHATBOT_APP = "CHATBOT_APP",
  KNOWLEDGE_BASE_APP = "KNOWLEDGE_BASE_APP",
  WORKFLOW_AUTOMATION_APP = "WORKFLOW_AUTOMATION_APP",
  DATA_ANALYSIS_APP = "DATA_ANALYSIS_APP",
  CONTENT_GENERATION_APP = "CONTENT_GENERATION_APP",
  CUSTOM_APP = "CUSTOM_APP",
}

// 申请状态枚举 - 与 Prisma 生成的类型保持一致
export enum ApplicationRequestStatus {
  DRAFT = "draft",
  SUBMITTED = "submitted",
  REVIEWING = "reviewing", // 注意：Prisma 中是 REVIEWING 而不是 UNDER_REVIEW
  APPROVED = "approved",
  REJECTED = "rejected",
  WITHDRAWN = "withdrawn", // 注意：Prisma 中是 WITHDRAWN 而不是 CANCELLED
  EXPIRED = "expired",
}

// 审批状态枚举
export enum ApprovalStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  CANCELLED = "CANCELLED",
}

// 审批决定枚举
export enum ApprovalDecision {
  APPROVE = "APPROVE",
  REJECT = "REJECT",
  REQUEST_CHANGES = "REQUEST_CHANGES",
}

// 租户角色枚举
export enum TenantRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  MEMBER = "MEMBER",
  VIEWER = "VIEWER",
}

// 租户类型标签映射
export const TenantTypeLabels: Record<TenantType, string> = {
  [TenantType.HOSPITAL_TERTIARY]: "三甲医院",
  [TenantType.HOSPITAL_SECONDARY]: "二甲医院",
  [TenantType.HOSPITAL_PRIMARY]: "一甲医院",
  [TenantType.HOSPITAL_SPECIALIZED]: "专科医院",
  [TenantType.CLINIC]: "诊所",
  [TenantType.HEALTH_CENTER]: "卫生院",
  [TenantType.MEDICAL_GROUP]: "医疗集团",
  [TenantType.OTHER]: "其他",
};

// 租户状态标签映射
export const TenantStatusLabels: Record<TenantStatus, string> = {
  [TenantStatus.PENDING]: "待审核",
  [TenantStatus.ACTIVE]: "活跃",
  [TenantStatus.INACTIVE]: "非活跃",
  [TenantStatus.SUSPENDED]: "暂停",
  [TenantStatus.REJECTED]: "拒绝",
  [TenantStatus.EXPIRED]: "过期",
};

// 租户状态颜色映射
export const TenantStatusColors: Record<TenantStatus, string> = {
  [TenantStatus.PENDING]: "bg-yellow-100 text-yellow-800",
  [TenantStatus.ACTIVE]: "bg-green-100 text-green-800",
  [TenantStatus.INACTIVE]: "bg-gray-100 text-gray-800",
  [TenantStatus.SUSPENDED]: "bg-red-100 text-red-800",
  [TenantStatus.REJECTED]: "bg-red-100 text-red-800",
  [TenantStatus.EXPIRED]: "bg-gray-100 text-gray-800",
};

// 租户接口定义
export interface Tenant {
  id: string;
  name: string;
  displayName?: string;
  description?: string;
  type: TenantType;
  category: string;
  contactName: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  website?: string;
  licenseNumber?: string;
  taxId?: string;
  legalPerson?: string;
  status: TenantStatus;
  isActive: boolean;
  isVerified: boolean;
  verifiedAt?: Date;
  suspendedAt?: Date;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// 租户注册表单数据
export interface TenantRegistrationFormData {
  name: string;
  displayName?: string;
  description?: string;
  type: TenantType;
  category: string;
  contactName: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  website?: string;
  licenseNumber?: string;
  taxId?: string;
  legalPerson?: string;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
}

// 租户统计数据
export interface TenantStats {
  totalTenants: number;
  activeTenants: number;
  pendingTenants: number;
  suspendedTenants: number;
  totalApplications: number;
}

// 租户列表查询参数
export interface TenantListParams {
  status?: TenantStatus;
  type?: TenantType;
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
}

// 租户列表响应
export interface TenantListResponse {
  tenants: Tenant[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}
