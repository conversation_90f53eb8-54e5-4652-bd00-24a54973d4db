/**
 * 租户管理系统演示脚本
 *
 * 这个脚本展示了租户管理系统的完整功能流程
 * 包括租户注册、审批、应用申请等核心功能
 */

import type {
  TenantType,
  TenantStatus,
  ApplicationType,
  ApprovalDecision,
} from "@langfuse/shared/src/db";

// 运行时枚举常量
const TenantTypeEnum = {
  HOSPITAL_TERTIARY: "HOSPITAL_TERTIARY" as const,
  HOSPITAL_SECONDARY: "HOSPITAL_SECONDARY" as const,
  HOSPITAL_PRIMARY: "HOSPITAL_PRIMARY" as const,
  CLINIC: "CLINIC" as const,
  HEALTH_CENTER: "HEALTH_CENTER" as const,
  PHARMACEUTICAL_COMPANY: "PHARMACEUTICAL_COMPANY" as const,
  MEDICAL_DEVICE_COMPANY: "MEDICAL_DEVICE_COMPANY" as const,
  RESEARCH_INSTITUTION: "RESEARCH_INSTITUTION" as const,
  GOVERNMENT_AGENCY: "GOVERNMENT_AGENCY" as const,
  INSURANCE_COMPANY: "INSURANCE_COMPANY" as const,
  TECHNOLOGY_COMPANY: "TECHNOLOGY_COMPANY" as const,
  OTHER: "OTHER" as const,
};

const TenantStatusEnum = {
  PENDING: "PENDING" as const,
  ACTIVE: "ACTIVE" as const,
  INACTIVE: "INACTIVE" as const,
  SUSPENDED: "SUSPENDED" as const,
  REJECTED: "REJECTED" as const,
};

const ApplicationTypeEnum = {
  ROBOT_APPLICATION: "ROBOT_APPLICATION" as const,
  QUALITY_CONTROL_APP: "QUALITY_CONTROL_APP" as const,
  DOCUMENT_GENERATION_APP: "DOCUMENT_GENERATION_APP" as const,
  INTELLIGENT_CUSTOMER_SERVICE: "INTELLIGENT_CUSTOMER_SERVICE" as const,
  INTELLIGENT_AGENT_APP: "INTELLIGENT_AGENT_APP" as const,
  CUSTOM_APPLICATION: "CUSTOM_APPLICATION" as const,
};

const ApprovalDecisionEnum = {
  APPROVED: "APPROVED" as const,
  REJECTED: "REJECTED" as const,
  PENDING: "PENDING" as const,
};

// 演示数据
export const demoTenantData = {
  name: "北京协和医院",
  displayName: "北京协和医院",
  description: "国内顶级综合性医院，医疗技术领先",
  type: TenantTypeEnum.HOSPITAL_TERTIARY,
  category: "综合医院",
  contactName: "张主任",
  contactEmail: "<EMAIL>",
  contactPhone: "010-69156114",
  address: "北京市东城区东单帅府园1号",
  website: "https://www.pumch.cn",
  licenseNumber: "PDY00001X440100002A1002",
  taxId: "12100000400001309D",
  legalPerson: "赵院长",
  settings: {
    maxUsers: 1000,
    enableNotifications: true,
    theme: "medical",
  },
  metadata: {
    establishedYear: 1921,
    bedCount: 2000,
    departments: ["内科", "外科", "妇产科", "儿科", "急诊科"],
  },
};

export const demoApplicationData = {
  applicationName: "智能诊断助手系统",
  applicationType: ApplicationTypeEnum.INTELLIGENT_AGENT_APP,
  description: "基于人工智能的医疗诊断辅助系统，帮助医生提高诊断准确率",
  businessCase: `
    项目背景：
    随着医疗需求的增长和医疗资源的相对稀缺，提高诊断效率和准确率成为医院发展的关键。
    
    项目目标：
    1. 提高诊断准确率至95%以上
    2. 减少误诊率至2%以下
    3. 缩短诊断时间30%
    4. 提升患者满意度
    
    预期效益：
    - 年节约医疗成本约500万元
    - 提升医院诊疗效率20%
    - 减少医疗纠纷50%
  `,
  expectedUsers: 500,
  applicantName: "李医生",
  applicantEmail: "<EMAIL>",
  applicantPhone: "138-0000-1234",
  attachments: [
    {
      fileName: "项目需求说明书.pdf",
      fileUrl: "https://example.com/files/requirements.pdf",
      fileSize: 2048000,
      fileType: "application/pdf",
    },
    {
      fileName: "技术方案书.docx",
      fileUrl: "https://example.com/files/technical-proposal.docx",
      fileSize: 1536000,
      fileType:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    },
    {
      fileName: "预算明细表.xlsx",
      fileUrl: "https://example.com/files/budget.xlsx",
      fileSize: 512000,
      fileType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    },
  ],
};

/**
 * 演示完整的租户注册流程
 */
export const demoTenantRegistrationFlow = async (api: any) => {
  console.log("🏥 开始租户注册演示流程...");

  try {
    // 1. 租户注册
    console.log("📝 步骤1: 提交租户注册申请");
    const registrationResult =
      await api.tenantManagement.tenant.register.mutateAsync(demoTenantData);
    console.log("✅ 注册成功:", registrationResult);

    const tenantId = registrationResult.id;

    // 2. 查看租户状态
    console.log("👀 步骤2: 查看租户详情");
    const tenantDetails = await api.tenantManagement.tenant.byId.query({
      tenantId,
    });
    console.log("📊 租户状态:", tenantDetails.status);

    // 3. 管理员审批（需要管理员权限）
    console.log("⚖️ 步骤3: 管理员审批租户");
    const approvalResult =
      await api.tenantManagement.tenant.updateStatus.mutateAsync({
        tenantId,
        status: TenantStatusEnum.ACTIVE,
        reason: "审核通过，资质齐全，符合平台要求",
      });
    console.log("✅ 审批完成:", approvalResult.status);

    // 4. 查看系统统计
    console.log("📈 步骤4: 查看系统统计");
    const systemStats = await api.tenantManagement.tenant.stats.query({});
    console.log("📊 系统统计:", systemStats);

    return { tenantId, success: true };
  } catch (error) {
    console.error("❌ 租户注册流程失败:", error);
    return { success: false, error };
  }
};

/**
 * 演示应用申请和审批流程
 */
export const demoApplicationFlow = async (api: any, tenantId: string) => {
  console.log("📱 开始应用申请演示流程...");

  try {
    // 1. 创建应用申请
    console.log("📝 步骤1: 创建应用申请");
    const applicationData = {
      ...demoApplicationData,
      tenantId,
    };

    const application =
      await api.tenantManagement.application.create.mutateAsync(
        applicationData,
      );
    console.log("✅ 申请创建成功:", application.id);

    // 2. 提交申请
    console.log("📤 步骤2: 提交申请");
    await api.tenantManagement.application.submit.mutateAsync({
      applicationId: application.id,
    });
    console.log("✅ 申请已提交，等待审批");

    // 3. 查看申请详情
    console.log("👀 步骤3: 查看申请详情");
    const applicationDetails =
      await api.tenantManagement.application.byId.query({
        applicationId: application.id,
      });
    console.log("📊 申请状态:", applicationDetails.status);

    // 4. 查看审批工作流
    console.log("🔄 步骤4: 查看审批工作流");
    const approvals = await api.tenantManagement.approval.list.query({
      tenantId,
      status: "PENDING",
    });
    console.log("📋 待审批任务数:", approvals.totalCount);

    // 5. 处理审批（需要审批权限）
    if (approvals.workflows && approvals.workflows.length > 0) {
      const firstApproval = approvals.workflows[0];
      console.log("⚖️ 步骤5: 处理审批");

      await api.tenantManagement.approval.process.mutateAsync({
        workflowId: firstApproval.id,
        decision: ApprovalDecisionEnum.APPROVED,
        comments: "申请材料完整，技术方案可行，业务需求明确，批准通过。",
      });
      console.log("✅ 初审通过");
    }

    // 6. 查看申请统计
    console.log("📈 步骤6: 查看申请统计");
    const applicationStats = await api.tenantManagement.application.stats.query(
      { tenantId },
    );
    console.log("📊 申请统计:", applicationStats);

    return { applicationId: application.id, success: true };
  } catch (error) {
    console.error("❌ 应用申请流程失败:", error);
    return { success: false, error };
  }
};

/**
 * 演示权限管理功能
 */
export const demoPermissionManagement = async (api: any, tenantId: string) => {
  console.log("🔐 开始权限管理演示...");

  try {
    // 1. 查看租户审计日志
    console.log("📋 步骤1: 查看审计日志");
    const auditLogs = await api.tenantManagement.tenant.auditLogs.query({
      tenantId,
      page: 0,
      limit: 10,
    });
    console.log("📊 审计日志条数:", auditLogs.totalCount);

    // 2. 查看我的待办任务
    console.log("✅ 步骤2: 查看我的待办任务");
    const myTasks = await api.tenantManagement.approval.myTasks.query({
      page: 0,
      limit: 5,
    });
    console.log("📋 我的待办任务:", myTasks.totalCount);

    // 3. 查看审批统计
    console.log("📈 步骤3: 查看审批统计");
    const approvalStats = await api.tenantManagement.approval.stats.query({
      tenantId,
    });
    console.log("📊 审批统计:", approvalStats);

    return { success: true };
  } catch (error) {
    console.error("❌ 权限管理演示失败:", error);
    return { success: false, error };
  }
};

/**
 * 完整的演示流程
 */
export const runCompleteDemo = async (api: any) => {
  console.log("🚀 开始租户管理系统完整演示...");
  console.log("=".repeat(50));

  // 1. 租户注册流程
  const tenantResult = await demoTenantRegistrationFlow(api);
  if (!tenantResult.success) {
    console.log("❌ 演示终止：租户注册失败");
    return;
  }

  console.log("=".repeat(50));

  // 2. 应用申请流程
  const applicationResult = await demoApplicationFlow(
    api,
    tenantResult.tenantId!,
  );
  if (!applicationResult.success) {
    console.log("❌ 演示终止：应用申请失败");
    return;
  }

  console.log("=".repeat(50));

  // 3. 权限管理演示
  const permissionResult = await demoPermissionManagement(
    api,
    tenantResult.tenantId!,
  );
  if (!permissionResult.success) {
    console.log("❌ 演示终止：权限管理失败");
    return;
  }

  console.log("=".repeat(50));
  console.log("🎉 租户管理系统演示完成！");
  console.log("✅ 所有功能模块运行正常");
  console.log("📊 系统已准备好投入生产使用");
};

/**
 * 使用示例
 */
export const demoUsageExample = `
// 在React组件中使用演示脚本
import { runCompleteDemo } from '@/src/features/tenant-management/demo/demo-script';
import { api } from '@/src/utils/api';

const DemoComponent = () => {
  const handleRunDemo = async () => {
    await runCompleteDemo(api);
  };

  return (
    <button onClick={handleRunDemo}>
      运行租户管理系统演示
    </button>
  );
};

// 在控制台中运行演示
// 1. 打开浏览器开发者工具
// 2. 在控制台中运行：
// runCompleteDemo(window.__NEXT_DATA__.props.pageProps.trpc);
`;

export default {
  demoTenantData,
  demoApplicationData,
  demoTenantRegistrationFlow,
  demoApplicationFlow,
  demoPermissionManagement,
  runCompleteDemo,
  demoUsageExample,
};
