import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { type TenantListParams } from "../types";

// 租户管理相关的 React Query hooks

// 租户注册
export const useTenantRegister = () => {
  return api.tenantManagement.tenant.register.useMutation();
};

// 获取租户列表
export const useTenantList = (params: TenantListParams) => {
  return api.tenantManagement.tenant.list.useQuery({
    ...params,
    status: params.status as any,
    type: params.type as any,
  });
};

// 获取租户详情
export const useTenant = (tenantId: string) => {
  return api.tenantManagement.tenant.byId.useQuery(
    { tenantId },
    { enabled: !!tenantId },
  );
};

// 更新租户信息
export const useTenantUpdate = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.update.useMutation({
    onSuccess: () => {
      toast.success("租户信息更新成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });

      // 刷新租户详情
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "byId"]],
      });
    },
    onError: (error: any) => {
      console.error("更新租户信息失败:", error);
      const message =
        error?.message || error?.data?.message || "更新租户信息失败";
      toast.error(message);
    },
  });
};

// 更新租户状态
export const useTenantUpdateStatus = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.updateStatus.useMutation({
    onSuccess: () => {
      toast.success("租户状态更新成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "stats"]],
      });
    },
    onError: (error: any) => {
      console.error("更新租户状态失败:", error);
      const message =
        error?.message || error?.data?.message || "更新租户状态失败";
      toast.error(message);
    },
  });
};

// 删除租户
export const useTenantDelete = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.delete.useMutation({
    onSuccess: (data) => {
      toast.success(data.message || "租户删除成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "stats"]],
      });
    },
    onError: (error: any) => {
      console.error("删除租户失败:", error);
      const message = error?.message || error?.data?.message || "删除租户失败";
      toast.error(message);
    },
  });
};

// 获取租户统计信息
export const useTenantStats = (tenantId?: string) => {
  return api.tenantManagement.tenant.stats.useQuery(
    { tenantId },
    { enabled: true },
  );
};

// 获取租户审计日志
export const useTenantAuditLogs = (params: {
  tenantId: string;
  page?: number;
  limit?: number;
  action?: string;
  resourceType?: string;
}) => {
  return api.tenantManagement.tenant.auditLogs.useQuery(params);
};

// 租户应用申请相关 hooks

// 创建租户应用申请
export const useTenantApplicationCreate = () => {
  return api.tenantManagement.application.create.useMutation();
};

// 获取租户应用申请列表
export const useTenantApplicationList = (params: {
  tenantId?: string;
  status?: string;
  applicationType?: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  return api.tenantManagement.application.list.useQuery({
    ...params,
    status: params.status as any,
    applicationType: params.applicationType as any,
  });
};

// 获取租户应用申请详情
export const useTenantApplication = (applicationId: string) => {
  return api.tenantManagement.application.byId.useQuery(
    { applicationId },
    { enabled: !!applicationId },
  );
};

// 更新租户应用申请
export const useTenantApplicationUpdate = () => {
  return api.tenantManagement.application.update.useMutation();
};

// 提交租户应用申请
export const useTenantApplicationSubmit = () => {
  return api.tenantManagement.application.submit.useMutation();
};

// 撤回租户应用申请
export const useTenantApplicationWithdraw = () => {
  return api.tenantManagement.application.withdraw.useMutation();
};

// 删除租户应用申请
export const useTenantApplicationDelete = () => {
  return api.tenantManagement.application.delete.useMutation();
};

// 获取租户应用申请统计信息
export const useTenantApplicationStats = (tenantId?: string) => {
  return api.tenantManagement.application.stats.useQuery(
    { tenantId },
    { enabled: true },
  );
};

// 审批相关 hooks

// 获取审批列表
export const useApprovalList = (params: {
  status?: string;
  assigneeId?: string;
  assigneeRole?: string;
  tenantId?: string;
  overdue?: boolean;
  page?: number;
  limit?: number;
}) => {
  return api.tenantManagement.approval.list.useQuery({
    ...params,
    status: params.status as any,
  });
};

// 获取审批详情
export const useApproval = (workflowId: string) => {
  return api.tenantManagement.approval.byId.useQuery(
    { workflowId },
    { enabled: !!workflowId },
  );
};

// 处理审批
export const useApprovalProcess = () => {
  return api.tenantManagement.approval.process.useMutation();
};

// 分配审批人
export const useApprovalAssign = () => {
  return api.tenantManagement.approval.assign.useMutation();
};

// 更新审批截止时间
export const useApprovalUpdateDueDate = () => {
  return api.tenantManagement.approval.updateDueDate.useMutation();
};

// 获取审批统计信息
export const useApprovalStats = (params: {
  tenantId?: string;
  assigneeId?: string;
}) => {
  return api.tenantManagement.approval.stats.useQuery(params);
};

// 获取我的待办审批
export const useMyApprovalTasks = (params: {
  page?: number;
  limit?: number;
}) => {
  return api.tenantManagement.approval.myTasks.useQuery(params);
};

// 批量处理审批 - 暂时注释掉，因为服务器端还没有实现
// export const useApprovalBatchProcess = () => {
//   return api.tenantManagement.approval.batchProcess.useMutation();
// };

// 组合 hooks

// 获取租户概览数据
export const useTenantOverview = (tenantId: string) => {
  const tenantQuery = useTenant(tenantId);
  const statsQuery = useTenantStats(tenantId);
  const applicationStatsQuery = useTenantApplicationStats(tenantId);
  const approvalStatsQuery = useApprovalStats({ tenantId });

  return {
    tenant: tenantQuery.data,
    stats: statsQuery.data,
    applicationStats: applicationStatsQuery.data,
    approvalStats: approvalStatsQuery.data,
    isLoading:
      tenantQuery.isLoading ||
      statsQuery.isLoading ||
      applicationStatsQuery.isLoading ||
      approvalStatsQuery.isLoading,
    error:
      tenantQuery.error ||
      statsQuery.error ||
      applicationStatsQuery.error ||
      approvalStatsQuery.error,
  };
};

// 获取租户管理仪表板数据
export const useTenantDashboard = () => {
  const systemStatsQuery = useTenantStats();
  const systemApplicationStatsQuery = useTenantApplicationStats();
  const systemApprovalStatsQuery = useApprovalStats({});
  const myTasksQuery = useMyApprovalTasks({ limit: 5 });

  return {
    systemStats: systemStatsQuery.data,
    systemApplicationStats: systemApplicationStatsQuery.data,
    systemApprovalStats: systemApprovalStatsQuery.data,
    myTasks: myTasksQuery.data,
    isLoading:
      systemStatsQuery.isLoading ||
      systemApplicationStatsQuery.isLoading ||
      systemApprovalStatsQuery.isLoading ||
      myTasksQuery.isLoading,
    error:
      systemStatsQuery.error ||
      systemApplicationStatsQuery.error ||
      systemApprovalStatsQuery.error ||
      myTasksQuery.error,
  };
};

// 实用工具 hooks

// 刷新租户相关数据
export const useRefreshTenantData = () => {
  const utils = api.useUtils();

  return {
    refreshTenant: (tenantId: string) => {
      utils.tenantManagement.tenant.byId.invalidate({ tenantId });
      utils.tenantManagement.tenant.stats.invalidate({ tenantId });
      utils.tenantManagement.tenant.auditLogs.invalidate({ tenantId });
    },
    refreshTenantList: () => {
      utils.tenantManagement.tenant.list.invalidate();
      utils.tenantManagement.tenant.stats.invalidate();
    },
    refreshApplications: (tenantId?: string) => {
      utils.tenantManagement.application.list.invalidate();
      utils.tenantManagement.application.stats.invalidate({ tenantId });
    },
    refreshApprovals: () => {
      utils.tenantManagement.approval.list.invalidate();
      utils.tenantManagement.approval.myTasks.invalidate();
      utils.tenantManagement.approval.stats.invalidate();
    },
  };
};

// 错误处理 hook
export const useTenantErrorHandler = () => {
  return {
    handleError: (error: any) => {
      console.error("Tenant management error:", error);
      // 这里可以添加错误上报逻辑
      // 例如：Sentry.captureException(error);
    },
    getErrorMessage: (error: any): string => {
      if (error?.message) {
        return error.message;
      }
      if (error?.data?.message) {
        return error.data.message;
      }
      return "操作失败，请稍后重试";
    },
  };
};
