import { type Role } from "@langfuse/shared/src/db";

const projectScopes = [
  "projectMembers:read",
  "projectMembers:CUD",

  "apiKeys:read",
  "apiKeys:CUD",

  "objects:publish",
  "objects:bookmark",
  "objects:tag",

  "traces:delete",

  "scores:CUD",

  "scoreConfigs:CUD",
  "scoreConfigs:read",

  "annotationQueues:read",
  "annotationQueues:CUD",
  "annotationQueueAssignments:read",
  "annotationQueueAssignments:CUD",

  "project:read",
  "project:update",
  "project:delete",

  "integrations:CRUD",

  "datasets:CUD",

  "prompts:CUD",
  "prompts:read",
  "promptProtectedLabels:CUD",

  "dashboards:read",
  "dashboards:CUD",

  "models:CUD",

  "batchExports:create",
  "batchExports:read",

  "evalTemplate:CUD",
  "evalTemplate:read",
  "evalJob:read",
  "evalJob:CUD",
  "evalJobExecution:read",
  "evalDefaultModel:read",
  "evalDefaultModel:CUD",

  "llmApiKeys:read",
  "llmApiKeys:create",
  "llmApiKeys:update",
  "llmApiKeys:delete",

  "llmSchemas:CUD",
  "llmSchemas:read",

  "llmTools:CUD",
  "llmTools:read",

  "comments:CUD",
  "comments:read",

  "promptExperiments:CUD",
  "promptExperiments:read",

  "auditLogs:read",

  "TableViewPresets:CUD",
  "TableViewPresets:read",

  "automations:CUD",
  "automations:read",
] as const;

// type string of all Resource:Action, e.g. "members:read"
export type ProjectScope = (typeof projectScopes)[number];

export const projectRoleAccessRights: Record<Role, ProjectScope[]> = {
  OWNER: [
    "project:read",
    "project:update",
    "project:delete",
    "projectMembers:read",
    "projectMembers:CUD",
    "apiKeys:read",
    "apiKeys:CUD",
    "integrations:CRUD",
    "objects:publish",
    "objects:bookmark",
    "objects:tag",
    "traces:delete",
    "scores:CUD",
    "scoreConfigs:CUD",
    "scoreConfigs:read",
    "datasets:CUD",
    "prompts:CUD",
    "prompts:read",
    "promptProtectedLabels:CUD",
    "models:CUD",
    "evalTemplate:CUD",
    "evalTemplate:read",
    "evalJob:CUD",
    "evalJob:read",
    "evalJobExecution:read",
    "evalDefaultModel:CUD",
    "evalDefaultModel:read",
    "llmApiKeys:read",
    "llmApiKeys:create",
    "llmApiKeys:update",
    "llmApiKeys:delete",
    "llmSchemas:CUD",
    "llmSchemas:read",
    "llmTools:CUD",
    "llmTools:read",
    "batchExports:create",
    "batchExports:read",
    "comments:CUD",
    "comments:read",
    "annotationQueues:read",
    "annotationQueues:CUD",
    "annotationQueueAssignments:read",
    "annotationQueueAssignments:CUD",
    "promptExperiments:CUD",
    "promptExperiments:read",
    "auditLogs:read",
    "dashboards:read",
    "dashboards:CUD",
    "TableViewPresets:CUD",
    "TableViewPresets:read",
    "automations:CUD",
    "automations:read",
  ],
  ADMIN: [
    "project:read",
    "project:update",
    "projectMembers:read",
    "projectMembers:CUD",
    "apiKeys:read",
    "apiKeys:CUD",
    "integrations:CRUD",
    "objects:publish",
    "objects:bookmark",
    "objects:tag",
    "traces:delete",
    "scores:CUD",
    "scoreConfigs:CUD",
    "scoreConfigs:read",
    "datasets:CUD",
    "prompts:CUD",
    "prompts:read",
    "promptProtectedLabels:CUD",
    "models:CUD",
    "evalTemplate:CUD",
    "evalTemplate:read",
    "evalJob:CUD",
    "evalJob:read",
    "evalJobExecution:read",
    "evalDefaultModel:CUD",
    "evalDefaultModel:read",
    "llmApiKeys:read",
    "llmApiKeys:create",
    "llmApiKeys:update",
    "llmApiKeys:delete",
    "llmSchemas:CUD",
    "llmSchemas:read",
    "llmTools:CUD",
    "llmTools:read",
    "batchExports:create",
    "batchExports:read",
    "comments:CUD",
    "comments:read",
    "annotationQueues:read",
    "annotationQueues:CUD",
    "annotationQueueAssignments:read",
    "annotationQueueAssignments:CUD",
    "promptExperiments:CUD",
    "promptExperiments:read",
    "auditLogs:read",
    "dashboards:read",
    "dashboards:CUD",
    "TableViewPresets:CUD",
    "TableViewPresets:read",
    "automations:CUD",
    "automations:read",
  ],
  MEMBER: [
    "project:read",
    "projectMembers:read",
    "apiKeys:read",
    "objects:publish",
    "objects:bookmark",
    "objects:tag",
    "scores:CUD",
    "scoreConfigs:CUD",
    "scoreConfigs:read",
    "datasets:CUD",
    "prompts:CUD",
    "prompts:read",
    "evalTemplate:CUD",
    "evalTemplate:read",
    "evalJob:read",
    "evalJob:CUD",
    "evalJobExecution:read",
    "evalDefaultModel:read",
    "evalDefaultModel:CUD",
    "llmApiKeys:read",
    "llmSchemas:read",
    "llmTools:read",
    "batchExports:create",
    "batchExports:read",
    "comments:CUD",
    "comments:read",
    "annotationQueues:read",
    "annotationQueues:CUD",
    "annotationQueueAssignments:read",
    "promptExperiments:CUD",
    "promptExperiments:read",
    "dashboards:read",
    "dashboards:CUD",
    "TableViewPresets:CUD",
    "TableViewPresets:read",
    "automations:read",
  ],
  VIEWER: [
    "project:read",
    "prompts:read",
    "evalTemplate:read",
    "scoreConfigs:read",
    "evalJob:read",
    "evalJobExecution:read",
    "evalDefaultModel:read",
    "llmApiKeys:read",
    "llmSchemas:read",
    "llmTools:read",
    "comments:read",
    "annotationQueues:read",
    "promptExperiments:read",
    "dashboards:read",
    "TableViewPresets:read",
    "automations:read",
  ],
  NONE: [],
};

export const projectNoneRoleComment =
  "Do not override the organization role for this project.";
