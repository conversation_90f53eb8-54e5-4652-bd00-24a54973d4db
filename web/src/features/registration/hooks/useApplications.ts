import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/router";
import { toast } from "sonner";
import type {
  ApplicationType,
  ApplicationStatus,
} from "@langfuse/shared/src/db";

// 运行时枚举常量
const ApplicationTypeEnum = {
  ROBOT_APPLICATION: "ROBOT_APPLICATION" as const,
  QUALITY_CONTROL_APP: "QUALITY_CONTROL_APP" as const,
  DOCUMENT_GENERATION_APP: "DOCUMENT_GENERATION_APP" as const,
  INTELLIGENT_CUSTOMER_SERVICE: "INTELLIGENT_CUSTOMER_SERVICE" as const,
  INTELLIGENT_AGENT_APP: "INTELLIGENT_AGENT_APP" as const,
  CUSTOM_APPLICATION: "CUSTOM_APPLICATION" as const,
};

const ApplicationStatusEnum = {
  PENDING: "PENDING" as const,
  ACTIVE: "ACTIVE" as const,
  INACTIVE: "INACTIVE" as const,
  SUSPENDED: "SUSPENDED" as const,
  REJECTED: "REJECTED" as const,
};

// 应用列表查询参数类型
export interface ApplicationListParams {
  projectId: string;
  status?: ApplicationStatus;
  type?: ApplicationType;
  category?: string;
  search?: string;
  limit?: number;
  page?: number;
}

// 创建应用参数类型
export interface CreateApplicationParams {
  projectId: string;
  name: string;
  description?: string;
  type: ApplicationType;
  category: string;
  version?: string;
  developer: string;
  tags?: string[];
  isPublic?: boolean;
  autoApprove?: boolean;
  serviceConfig?: {
    endpoint?: string;
    timeout?: number;
    retryCount?: number;
    rateLimit?: number;
  };
  permissions?: string[];
}

// 更新应用参数类型
export interface UpdateApplicationParams {
  applicationId: string;
  name?: string;
  description?: string;
  version?: string;
  developer?: string;
  tags?: string[];
  isPublic?: boolean;
  autoApprove?: boolean;
  serviceConfig?: {
    endpoint?: string;
    timeout?: number;
    retryCount?: number;
    rateLimit?: number;
  };
  permissions?: string[];
  status?: ApplicationStatus;
}

// 获取应用列表
export function useApplications(params: ApplicationListParams) {
  return api.applications.list.useQuery(params, {
    enabled: !!params.projectId,
    placeholderData: (previousData) => previousData,
  });
}

// 获取单个应用详情
export function useApplication(projectId: string, applicationId: string) {
  return api.applications.byId.useQuery(
    { projectId, applicationId },
    {
      enabled: !!projectId && !!applicationId,
    },
  );
}

// 获取应用统计信息
export function useApplicationStats(projectId: string) {
  return api.applications.stats.useQuery(
    { projectId },
    {
      enabled: !!projectId,
    },
  );
}

// 创建应用
export function useCreateApplication() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return api.applications.create.useMutation({
    onSuccess: (data) => {
      toast.success("应用创建成功");

      // 刷新应用列表
      queryClient.invalidateQueries({
        queryKey: [["applications", "list"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["applications", "stats"]],
      });

      // 可选：跳转到应用详情页
      // router.push(`/project/${data.projectId}/registration/applications/${data.id}`);
    },
    onError: (error) => {
      toast.error(error.message || "创建应用失败");
    },
  });
}

// 更新应用
export function useUpdateApplication() {
  const queryClient = useQueryClient();

  return api.applications.update.useMutation({
    onSuccess: (data) => {
      toast.success("应用更新成功");

      // 刷新应用列表
      queryClient.invalidateQueries({
        queryKey: [["applications", "list"]],
      });

      // 刷新应用详情
      queryClient.invalidateQueries({
        queryKey: [["applications", "byId"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["applications", "stats"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "更新应用失败");
    },
  });
}

// 删除应用
export function useDeleteApplication() {
  const queryClient = useQueryClient();

  return api.applications.delete.useMutation({
    onSuccess: () => {
      toast.success("应用删除成功");

      // 刷新应用列表
      queryClient.invalidateQueries({
        queryKey: [["applications", "list"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["applications", "stats"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "删除应用失败");
    },
  });
}

// 审核应用
export function useApproveApplication() {
  const queryClient = useQueryClient();

  return api.applications.approve.useMutation({
    onSuccess: () => {
      toast.success("应用审核成功");

      // 刷新应用列表
      queryClient.invalidateQueries({
        queryKey: [["applications", "list"]],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [["applications", "stats"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "应用审核失败");
    },
  });
}

// 应用类型选项
export const APPLICATION_TYPE_OPTIONS = [
  {
    value: ApplicationTypeEnum.ROBOT_APPLICATION,
    label: "机器人应用",
    description: "智能语音识别和翻译服务",
    category: "AI服务",
  },
  {
    value: ApplicationTypeEnum.QUALITY_CONTROL_APP,
    label: "质控应用",
    description: "医疗病历质量控制和审核系统",
    category: "医疗健康",
  },
  {
    value: ApplicationTypeEnum.DOCUMENT_GENERATION_APP,
    label: "文书生成应用",
    description: "基于AI的医疗文书自动生成和编辑",
    category: "医疗健康",
  },
  {
    value: ApplicationTypeEnum.INTELLIGENT_CUSTOMER_SERVICE,
    label: "智能客服系统",
    description: "AI驱动的客户服务和支持平台",
    category: "客户服务",
  },
  {
    value: ApplicationTypeEnum.INTELLIGENT_AGENT_APP,
    label: "智能体应用",
    description: "社区健康管理智能代理系统",
    category: "医疗健康",
  },
  {
    value: ApplicationTypeEnum.CUSTOM_APPLICATION,
    label: "自定义应用",
    description: "根据特定需求定制开发的应用程序",
    category: "自定义",
  },
];

// 应用状态选项
export const APPLICATION_STATUS_OPTIONS = [
  {
    value: ApplicationStatusEnum.PENDING,
    label: "待审核",
    color: "yellow",
  },
  {
    value: ApplicationStatusEnum.ACTIVE,
    label: "活跃",
    color: "green",
  },
  {
    value: ApplicationStatusEnum.INACTIVE,
    label: "非活跃",
    color: "gray",
  },
  {
    value: ApplicationStatusEnum.SUSPENDED,
    label: "已暂停",
    color: "red",
  },
  {
    value: ApplicationStatusEnum.REJECTED,
    label: "已拒绝",
    color: "red",
  },
];

// 获取应用状态显示信息
export function getApplicationStatusInfo(status: ApplicationStatus) {
  return (
    APPLICATION_STATUS_OPTIONS.find((option) => option.value === status) || {
      value: status,
      label: status,
      color: "gray",
    }
  );
}

// 获取应用类型显示信息
export function getApplicationTypeInfo(type: ApplicationType) {
  return (
    APPLICATION_TYPE_OPTIONS.find((option) => option.value === type) || {
      value: type,
      label: type,
      description: "",
      category: "其他",
    }
  );
}
