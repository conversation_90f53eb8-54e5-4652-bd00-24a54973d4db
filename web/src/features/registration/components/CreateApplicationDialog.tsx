import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod/v4";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import {
  useCreateApplication,
  APPLICATION_TYPE_OPTIONS,
} from "../hooks/useApplications";
import type { ApplicationType } from "@langfuse/shared/src/db";

// 运行时枚举常量
const ApplicationTypeEnum = {
  ROBOT_APPLICATION: "ROBOT_APPLICATION" as const,
  QUALITY_CONTROL_APP: "QUALITY_CONTROL_APP" as const,
  DOCUMENT_GENERATION_APP: "DOCUMENT_GENERATION_APP" as const,
  INTELLIGENT_CUSTOMER_SERVICE: "INTELLIGENT_CUSTOMER_SERVICE" as const,
  INTELLIGENT_AGENT_APP: "INTELLIGENT_AGENT_APP" as const,
  CUSTOM_APPLICATION: "CUSTOM_APPLICATION" as const,
};
import { Button } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogBody,
} from "@/src/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Badge } from "@/src/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Switch } from "@/src/components/ui/switch";
import {
  AppWindow,
  Bot,
  FileText,
  Stethoscope,
  MessageSquare,
  Building2,
  Plus,
  X,
} from "lucide-react";

// 应用类型图标映射
const getAppIcon = (type: ApplicationType) => {
  const iconMap: Record<ApplicationType, any> = {
    [ApplicationTypeEnum.ROBOT_APPLICATION]: Bot,
    [ApplicationTypeEnum.QUALITY_CONTROL_APP]: FileText,
    [ApplicationTypeEnum.DOCUMENT_GENERATION_APP]: Stethoscope,
    [ApplicationTypeEnum.INTELLIGENT_CUSTOMER_SERVICE]: MessageSquare,
    [ApplicationTypeEnum.INTELLIGENT_AGENT_APP]: Building2,
    [ApplicationTypeEnum.CUSTOM_APPLICATION]: AppWindow,
  };
  return iconMap[type] || AppWindow;
};

// 获取应用类型的默认标签
const getDefaultTagsForType = (type: ApplicationType): string[] => {
  const tagMap: Record<ApplicationType, string[]> = {
    [ApplicationTypeEnum.ROBOT_APPLICATION]: ["语音识别", "机器翻译", "多语言"],
    [ApplicationTypeEnum.QUALITY_CONTROL_APP]: [
      "病历管理",
      "质量控制",
      "医疗审核",
    ],
    [ApplicationTypeEnum.DOCUMENT_GENERATION_APP]: [
      "自动生成",
      "文书编辑",
      "AI辅助",
    ],
    [ApplicationTypeEnum.INTELLIGENT_CUSTOMER_SERVICE]: [
      "智能客服",
      "自动回复",
      "客户支持",
    ],
    [ApplicationTypeEnum.INTELLIGENT_AGENT_APP]: [
      "社区健康",
      "智能代理",
      "健康管理",
    ],
    [ApplicationTypeEnum.CUSTOM_APPLICATION]: [
      "定制开发",
      "特定需求",
      "灵活配置",
    ],
  };
  return tagMap[type] || [];
};

// 表单验证模式
const createApplicationSchema = (t: any) =>
  z.object({
    name: z.string().min(1, t("validation:required", "此字段为必填项")),
    description: z.string().optional(),
    type: z
      .nativeEnum(ApplicationTypeEnum)
      .optional()
      .refine((val) => val !== undefined, {
        message: t("validation:required", "请选择应用类型"),
      }),
    category: z.string().min(1, t("validation:required", "请选择应用分类")),
    version: z.string().min(1, t("validation:required", "此字段为必填项")),
    developer: z.string().min(1, t("validation:required", "此字段为必填项")),
    tags: z.array(z.string()).optional(),
    isPublic: z.boolean(),
    autoApprove: z.boolean(),
    serviceConfig: z
      .object({
        endpoint: z
          .string()
          .optional()
          .refine(
            (val) =>
              !val || val === "" || z.string().url().safeParse(val).success,
            {
              message: t("validation:invalidUrl", "请输入有效的URL"),
            },
          ),
        timeout: z.number().min(1000).max(300000).optional(),
        retryCount: z.number().min(0).max(10).optional(),
      })
      .optional(),
  });

type CreateApplicationFormData = z.infer<
  ReturnType<typeof createApplicationSchema>
>;

interface CreateApplicationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (application: any) => void;
}

export function CreateApplicationDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateApplicationDialogProps) {
  const { t } = useTranslation(["common", "registration", "validation"]);
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const [selectedType, setSelectedType] = useState<ApplicationType | "">("");
  const [customTags, setCustomTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");

  const createApplication = useCreateApplication();

  const form = useForm<CreateApplicationFormData>({
    resolver: zodResolver(createApplicationSchema(t)),
    defaultValues: {
      name: "",
      description: "",
      type: undefined,
      category: "",
      version: "1.0.0",
      developer: "",
      tags: [],
      isPublic: false,
      autoApprove: false,
      serviceConfig: {
        endpoint: "",
        timeout: 30000,
        retryCount: 3,
      },
    },
  });

  const selectedAppType = APPLICATION_TYPE_OPTIONS.find(
    (option) => option.value === selectedType,
  );

  const handleTypeSelect = (type: ApplicationType) => {
    const appType = APPLICATION_TYPE_OPTIONS.find(
      (option) => option.value === type,
    );
    if (appType) {
      setSelectedType(type);
      form.setValue("type", type);
      form.setValue("category", appType.category);
      form.setValue("name", appType.label);
      form.setValue("description", appType.description);
      // 根据应用类型设置默认标签
      const defaultTags = getDefaultTagsForType(type);
      setCustomTags(defaultTags);
      form.setValue("tags", defaultTags);
    }
  };

  const addCustomTag = () => {
    if (newTag.trim() && !customTags.includes(newTag.trim())) {
      const updatedTags = [...customTags, newTag.trim()];
      setCustomTags(updatedTags);
      form.setValue("tags", updatedTags);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const updatedTags = customTags.filter((tag) => tag !== tagToRemove);
    setCustomTags(updatedTags);
    form.setValue("tags", updatedTags);
  };

  const onSubmit = async (data: CreateApplicationFormData) => {
    if (!projectId) return;
    if (!data.type) {
      form.setError("type", {
        message: t("validation:required", "请选择应用类型"),
      });
      return;
    }

    try {
      // 处理空字符串，转换为undefined
      const processedData = {
        ...data,
        type: data.type, // 确保 type 有值
        serviceConfig: data.serviceConfig
          ? {
              ...data.serviceConfig,
              endpoint:
                data.serviceConfig.endpoint === ""
                  ? undefined
                  : data.serviceConfig.endpoint,
            }
          : undefined,
      };

      const result = await createApplication.mutateAsync({
        projectId,
        ...processedData,
      });

      // 成功回调
      onSuccess?.(result);
      onOpenChange(false);
      form.reset();
      setSelectedType("");
      setCustomTags([]);
    } catch (error) {
      console.error("Failed to create application:", error);
      // 错误处理已在hook中处理
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AppWindow className="h-5 w-5" />
            {t("registration:applications.newApplication", "新建应用")}
          </DialogTitle>
          <DialogDescription>
            选择应用类型并填写基本信息来注册新的应用程序
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogBody>
              {/* 应用类型选择 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">选择应用类型</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {APPLICATION_TYPE_OPTIONS.map((appType) => {
                    const Icon = getAppIcon(appType.value);
                    const defaultTags = getDefaultTagsForType(appType.value);
                    return (
                      <Card
                        key={appType.value}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedType === appType.value
                            ? "border-primary ring-2 ring-primary"
                            : ""
                        }`}
                        onClick={() => handleTypeSelect(appType.value)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center gap-3">
                            <Icon className="h-8 w-8 text-primary" />
                            <div>
                              <CardTitle className="text-base">
                                {appType.label}
                              </CardTitle>
                              <CardDescription className="text-sm">
                                {appType.description}
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-1">
                            {defaultTags.map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              {selectedType && (
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-lg font-medium">应用基本信息</h3>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>应用名称</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="输入应用名称" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="version"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>版本号</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="1.0.0" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>应用描述</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="详细描述应用的功能和用途"
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="developer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>开发商</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="输入开发商名称" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 标签管理 */}
                  <div className="space-y-2">
                    <FormLabel>应用标签</FormLabel>
                    <div className="mb-2 flex flex-wrap gap-2">
                      {customTags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="添加自定义标签"
                        onKeyPress={(e) =>
                          e.key === "Enter" &&
                          (e.preventDefault(), addCustomTag())
                        }
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addCustomTag}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* 应用配置 */}
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="font-medium">应用配置</h4>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <FormLabel>公开应用</FormLabel>
                          <FormDescription>
                            允许其他用户发现和使用此应用
                          </FormDescription>
                        </div>
                        <FormField
                          control={form.control}
                          name="isPublic"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <FormLabel>自动审批</FormLabel>
                          <FormDescription>
                            自动审批用户的使用申请
                          </FormDescription>
                        </div>
                        <FormField
                          control={form.control}
                          name="autoApprove"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </DialogBody>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                {t("common:cancel", "取消")}
              </Button>
              <Button
                type="submit"
                disabled={!selectedType || createApplication.isPending}
              >
                {createApplication.isPending ? "创建中..." : "创建应用"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
