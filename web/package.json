{"name": "web", "version": "3.103.0", "private": true, "license": "MIT", "engines": {"node": "24"}, "scripts": {"build": "INLINE_RUNTIME_CHUNK=false dotenv -e ../.env -- next build", "dev": "dotenv -e ../.env -- next dev", "dev:http": "dotenv -e ../.env -- next dev --experimental-https --experimental-https-key ./localhost+1-key.pem --experimental-https-cert ./localhost+1.pem", "lint": "dotenv -e ../.env -- next lint --max-warnings 0", "lint:fix": "dotenv -e ../.env -- next lint --fix", "clean": "rm -rf node_modules", "start": "dotenv -e ../.env -- sh -c 'NEXT_MANUAL_SIG_HANDLE=true next start'", "test": "cross-env NODE_OPTIONS='--no-experimental-require-module' dotenv -e ../.env.test -e ../.env -- jest --verbose --runInBand --detectOpenHandles --selectProjects async-server", "test-sync": "cross-env NODE_OPTIONS='--no-experimental-require-module' dotenv -e ../.env.test -e ../.env -- jest --verbose --runInBand --detectOpenHandles --selectProjects sync-server", "test-client": "cross-env NODE_OPTIONS='--no-experimental-require-module' dotenv -e ../.env.test -e ../.env -- jest --verbose --runInBand --detectOpenHandles --selectProjects client", "test:watch": "cross-env NODE_OPTIONS='--no-experimental-require-module' dotenv -e ../.env.test -e ../.env -- jest --watch --runInBand", "test:e2e": "dotenv -e ../.env.test -e ../.env -- playwright test --reporter=line", "test:e2e:server": "cross-env NODE_OPTIONS='--no-experimental-require-module' dotenv -e ../.env.test -e ../.env -- jest --runInBand --detectOpenHandles --verbose --selectProjects e2e-server"}, "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@appsignal/opentelemetry-instrumentation-bullmq": "^0.7.3", "@baselime/trpc-opentelemetry-middleware": "^0.1.2", "@codemirror/lang-json": "^6.0.2", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "2.1.9", "@headlessui/tailwindcss": "0.2.1", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.1.1", "@langchain/core": "^0.3.58", "@langfuse/ee": "workspace:*", "@langfuse/shared": "workspace:*", "@lezer/highlight": "^1.2.1", "@marsidev/react-turnstile": "^0.5.4", "@mui/x-tree-view": "^7.19.0", "@next-auth/prisma-adapter": "^1.0.7", "@opentelemetry/api": "^1.9.0", "@opentelemetry/core": "^1.26.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.53.0", "@opentelemetry/instrumentation": "^0.53.0", "@opentelemetry/instrumentation-aws-sdk": "^0.44.0", "@opentelemetry/instrumentation-http": "^0.53.0", "@opentelemetry/instrumentation-ioredis": "^0.43.0", "@opentelemetry/instrumentation-winston": "^0.40.0", "@opentelemetry/resource-detector-aws": "^1.6.2", "@opentelemetry/resource-detector-container": "^0.4.2", "@opentelemetry/resources": "^1.26.0", "@opentelemetry/sdk-node": "^0.53.0", "@opentelemetry/sdk-trace-base": "^1.26.0", "@opentelemetry/sdk-trace-node": "^1.26.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/instrumentation": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.2.0", "@sentry/nextjs": "^8.52.0", "@t3-oss/env-nextjs": "^0.11.1", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.85.1", "@tanstack/react-table": "^8.20.5", "@team-plain/typescript-sdk": "^5.8.0", "@tremor/react": "3.16.2", "@trpc/client": "^11.4.4", "@trpc/next": "^11.4.4", "@trpc/react-query": "^11.4.4", "@trpc/server": "^11.4.4", "@uiw/codemirror-themes": "^4.23.7", "@uiw/react-codemirror": "^4.24.1", "ai": "^3.4.9", "bcryptjs": "^2.4.3", "bullmq": "^5.34.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "core-js": "^3.38.1", "cors": "^2.8.5", "csv-parse": "^5.6.0", "date-fns": "^3.3.1", "dd-trace": "^5.36.0", "decimal.js": "^10.4.3", "diff": "^7.0.0", "dompurify": "^3.2.4", "fastest-levenshtein": "^1.0.16", "graphql": "^16.9.0", "https-proxy-agent": "^7.0.6", "ioredis": "^5.4.1", "ip-address": "^9.0.5", "kysely": "^0.27.4", "langchain": "^0.3.28", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "next": "^14.2.30", "next-auth": "^4.24.11", "next-query-params": "^5.0.1", "next-themes": "^0.3.0", "posthog-js": "^1.176.0", "posthog-node": "^4.3.1", "prexit": "^2.2.0", "prism-react-renderer": "^2.4.1", "prisma": "^6.10.1", "protobufjs": "^7.4.0", "rate-limiter-flexible": "^5.0.3", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.1", "react-responsive": "^10.0.0", "react18-json-view": "^0.2.8-canary.6", "recharts": "^2.15.2", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.1", "remark-stringify": "^11.0.0", "sonner": "^1.4.41", "stripe": "^17.4.0", "superjson": "2.2.2", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "use-query-params": "^2.2.1", "uuid": "^9.0.1", "vaul": "^1.1.2", "vis-network": "^9.1.9", "zod": "^3.25.62"}, "devDependencies": {"@jedmao/location": "^3.0.0", "@mermaid-js/mermaid-cli": "^11.2.0", "@next/bundle-analyzer": "^15.4.1", "@playwright/test": "^1.47.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/forms": "^0.5.7", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^15.0.7", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/diff": "^7.0.0", "@types/dompurify": "^3.0.5", "@types/eslint": "^8.56.7", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.10", "@types/node": "24.3.0", "@types/react": "~18.2.79", "@types/react-dom": "~18.2.25", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^7.12.0", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "dotenv-cli": "^7.4.2", "eslint": "^8.56.0", "eslint-config-next": "^14.2.15", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-mocks-http": "^1.14.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2", "wait-for-expect": "^3.0.2"}, "ct3aMetadata": {"initVersion": "7.13.0"}}